"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/analytics/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/analytics/page.tsx":
/*!**********************************************!*\
  !*** ./src/app/dashboard/analytics/page.tsx ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AnalyticsPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _services_analyticsService__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/services/analyticsService */ \"(app-pages-browser)/./src/services/analyticsService.ts\");\n/* harmony import */ var _components_analytics_PerformanceOverview__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/analytics/PerformanceOverview */ \"(app-pages-browser)/./src/components/analytics/PerformanceOverview.tsx\");\n/* harmony import */ var _components_analytics_PerformanceCharts__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/analytics/PerformanceCharts */ \"(app-pages-browser)/./src/components/analytics/PerformanceCharts.tsx\");\n/* harmony import */ var _components_analytics_InsightsPanel__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/analytics/InsightsPanel */ \"(app-pages-browser)/./src/components/analytics/InsightsPanel.tsx\");\n/* harmony import */ var _components_analytics_BenchmarkComparison__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/analytics/BenchmarkComparison */ \"(app-pages-browser)/./src/components/analytics/BenchmarkComparison.tsx\");\n/* harmony import */ var _components_analytics_GoalsTracker__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/analytics/GoalsTracker */ \"(app-pages-browser)/./src/components/analytics/GoalsTracker.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Award,BarChart3,Brain,Calendar,CheckCircle,Clock,Download,RefreshCw,Share2,Target,TrendingUp,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction AnalyticsPage() {\n    _s();\n    const [analyticsData, setAnalyticsData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [timeRange, setTimeRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"30d\");\n    const [selectedCategory, setSelectedCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"all\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        loadAnalyticsData();\n    }, [\n        timeRange\n    ]);\n    const loadAnalyticsData = async ()=>{\n        try {\n            setIsLoading(true);\n            const data = await _services_analyticsService__WEBPACK_IMPORTED_MODULE_5__.analyticsService.getAnalyticsData(\"user-123\", timeRange);\n            // Generate AI insights\n            const insights = await _services_analyticsService__WEBPACK_IMPORTED_MODULE_5__.analyticsService.generateInsights(data.performance);\n            data.insights = insights;\n            setAnalyticsData(data);\n        } catch (error) {\n            console.error(\"Error loading analytics data:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const exportReport = ()=>{\n        console.log(\"Exporting analytics report...\");\n    // Implement PDF export functionality\n    };\n    const shareReport = ()=>{\n        console.log(\"Sharing analytics report...\");\n    // Implement sharing functionality\n    };\n    const getTimeRangeLabel = (range)=>{\n        switch(range){\n            case \"7d\":\n                return \"Last 7 Days\";\n            case \"30d\":\n                return \"Last 30 Days\";\n            case \"90d\":\n                return \"Last 3 Months\";\n            case \"1y\":\n                return \"Last Year\";\n            default:\n                return \"Last 30 Days\";\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 70) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const getTrendIcon = (trend)=>{\n        switch(trend){\n            case \"improving\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 16\n                }, this);\n            case \"declining\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-red-600 rotate-180\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-8 w-8 animate-spin text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"ml-2\",\n                    children: \"Loading analytics...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n            lineNumber: 99,\n            columnNumber: 7\n        }, this);\n    }\n    if (!analyticsData) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-lg font-medium text-gray-600 mb-2\",\n                    children: \"No Analytics Data\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-500 mb-4\",\n                    children: \"Unable to load analytics data.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                    onClick: loadAnalyticsData,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"mr-2 h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 11\n                        }, this),\n                        \"Retry\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-600 flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Performance Analytics\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"Comprehensive insights into your interview performance and progress\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4 text-gray-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        value: timeRange,\n                                        onChange: (e)=>setTimeRange(e.target.value),\n                                        className: \"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"7d\",\n                                                children: \"Last 7 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"30d\",\n                                                children: \"Last 30 Days\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"90d\",\n                                                children: \"Last 3 Months\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"1y\",\n                                                children: \"Last Year\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 145,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                onClick: shareReport,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Share\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"outline\",\n                                onClick: exportReport,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                onClick: loadAnalyticsData,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Refresh\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Average Score\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold \".concat(getScoreColor(analyticsData.overview.averageScore)),\n                                        children: [\n                                            analyticsData.overview.averageScore,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1 text-xs text-muted-foreground\",\n                                        children: [\n                                            getTrendIcon(analyticsData.trends.performanceTrend),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"+\",\n                                                    analyticsData.overview.improvementRate,\n                                                    \"% from last period\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 175,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 166,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Total Sessions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: analyticsData.overview.totalSessions\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            analyticsData.overview.completionRate,\n                                            \"% completion rate\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 182,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Time Practiced\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold\",\n                                        children: [\n                                            Math.floor(analyticsData.overview.timeSpent / 60),\n                                            \"h \",\n                                            analyticsData.overview.timeSpent % 60,\n                                            \"m\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            analyticsData.overview.streakDays,\n                                            \" day streak\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 195,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Industry Rank\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 212,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-4 w-4 text-muted-foreground\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 211,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-2xl font-bold text-blue-600\",\n                                        children: [\n                                            analyticsData.comparisons.ranking.overall,\n                                            \"%\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: [\n                                            \"Top \",\n                                            100 - analyticsData.comparisons.ranking.overall,\n                                            \"% of candidates\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 165,\n                columnNumber: 7\n            }, this),\n            analyticsData.insights.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                className: \"border-blue-200 bg-gradient-to-r from-blue-50 to-purple-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                    className: \"pt-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-start space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-8 w-8 text-blue-600 mt-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-blue-900 mb-2\",\n                                        children: \"AI-Powered Insights\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: analyticsData.insights.slice(0, 2).map((insight)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    insight.type === \"achievement\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600 mt-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-yellow-600 mt-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium text-blue-900\",\n                                                                children: insight.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 245,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-blue-700\",\n                                                                children: insight.description\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                                lineNumber: 246,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, insight.id, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 230,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.Tabs, {\n                defaultValue: \"overview\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsList, {\n                        className: \"grid w-full grid-cols-5\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"overview\",\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 260,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"performance\",\n                                children: \"Performance\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"insights\",\n                                children: \"AI Insights\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 262,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"benchmarks\",\n                                children: \"Benchmarks\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsTrigger, {\n                                value: \"goals\",\n                                children: \"Goals\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 264,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_PerformanceOverview__WEBPACK_IMPORTED_MODULE_6__.PerformanceOverview, {\n                            data: analyticsData\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 267,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"performance\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_PerformanceCharts__WEBPACK_IMPORTED_MODULE_7__.PerformanceCharts, {\n                            data: analyticsData.performance,\n                            timeRange: timeRange\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"insights\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_InsightsPanel__WEBPACK_IMPORTED_MODULE_8__.InsightsPanel, {\n                            insights: analyticsData.insights,\n                            trends: analyticsData.trends\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 275,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"benchmarks\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_BenchmarkComparison__WEBPACK_IMPORTED_MODULE_9__.BenchmarkComparison, {\n                            userScore: analyticsData.overview.averageScore,\n                            comparisons: analyticsData.comparisons,\n                            categoryBreakdown: analyticsData.performance.categoryBreakdown\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                            lineNumber: 280,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_4__.TabsContent, {\n                        value: \"goals\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_analytics_GoalsTracker__WEBPACK_IMPORTED_MODULE_10__.GoalsTracker, {\n                            goals: analyticsData.goals\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 287,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 258,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Award_BarChart3_Brain_Calendar_CheckCircle_Clock_Download_RefreshCw_Share2_Target_TrendingUp_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"h-5 w-5 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Recommended Actions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                        lineNumber: 297,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"Based on your performance analysis, here are some recommended next steps\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-2\",\n                                            children: [\n                                                \"Practice \",\n                                                analyticsData.trends.weakestCategory\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3\",\n                                            children: \"Focus on your weakest area to see the biggest improvement\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 307,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: \"Start Practice\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 310,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-2\",\n                                            children: \"Schedule Regular Sessions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3\",\n                                            children: [\n                                                \"Maintain your \",\n                                                analyticsData.overview.streakDays,\n                                                \"-day streak with consistent practice\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: \"Set Schedule\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border rounded-lg\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium mb-2\",\n                                            children: \"Join Study Group\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-600 mb-3\",\n                                            children: \"Connect with peers in your industry for collaborative learning\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"sm\",\n                                            variant: \"outline\",\n                                            children: \"Find Groups\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                        lineNumber: 303,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n                lineNumber: 293,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\analytics\\\\page.tsx\",\n        lineNumber: 121,\n        columnNumber: 5\n    }, this);\n}\n_s(AnalyticsPage, \"s43pJJbZExAyGCv3asVVlj6e+6o=\");\n_c = AnalyticsPage;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZGFzaGJvYXJkL2FuYWx5dGljcy9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ0k7QUFDaUQ7QUFFakI7QUFDRjtBQUNHO0FBQ0o7QUFDUjtBQUNZO0FBQ2Q7QUFrQjdDO0FBRU4sU0FBUytCOztJQUN0QixNQUFNLENBQUNDLGVBQWVDLGlCQUFpQixHQUFHakMsK0NBQVFBLENBQXVCO0lBQ3pFLE1BQU0sQ0FBQ2tDLFdBQVdDLGFBQWEsR0FBR25DLCtDQUFRQSxDQUFDO0lBQzNDLE1BQU0sQ0FBQ29DLFdBQVdDLGFBQWEsR0FBR3JDLCtDQUFRQSxDQUE4QjtJQUN4RSxNQUFNLENBQUNzQyxrQkFBa0JDLG9CQUFvQixHQUFHdkMsK0NBQVFBLENBQVM7SUFFakVDLGdEQUFTQSxDQUFDO1FBQ1J1QztJQUNGLEdBQUc7UUFBQ0o7S0FBVTtJQUVkLE1BQU1JLG9CQUFvQjtRQUN4QixJQUFJO1lBQ0ZMLGFBQWE7WUFDYixNQUFNTSxPQUFPLE1BQU03Qix3RUFBZ0JBLENBQUM4QixnQkFBZ0IsQ0FBQyxZQUFZTjtZQUVqRSx1QkFBdUI7WUFDdkIsTUFBTU8sV0FBVyxNQUFNL0Isd0VBQWdCQSxDQUFDZ0MsZ0JBQWdCLENBQUNILEtBQUtJLFdBQVc7WUFDekVKLEtBQUtFLFFBQVEsR0FBR0E7WUFFaEJWLGlCQUFpQlE7UUFDbkIsRUFBRSxPQUFPSyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxpQ0FBaUNBO1FBQ2pELFNBQVU7WUFDUlgsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNYSxlQUFlO1FBQ25CRCxRQUFRRSxHQUFHLENBQUM7SUFDWixxQ0FBcUM7SUFDdkM7SUFFQSxNQUFNQyxjQUFjO1FBQ2xCSCxRQUFRRSxHQUFHLENBQUM7SUFDWixrQ0FBa0M7SUFDcEM7SUFFQSxNQUFNRSxvQkFBb0IsQ0FBQ0M7UUFDekIsT0FBUUE7WUFDTixLQUFLO2dCQUFNLE9BQU87WUFDbEIsS0FBSztnQkFBTyxPQUFPO1lBQ25CLEtBQUs7Z0JBQU8sT0FBTztZQUNuQixLQUFLO2dCQUFNLE9BQU87WUFDbEI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsTUFBTUMsZ0JBQWdCLENBQUNDO1FBQ3JCLElBQUlBLFNBQVMsSUFBSSxPQUFPO1FBQ3hCLElBQUlBLFNBQVMsSUFBSSxPQUFPO1FBQ3hCLE9BQU87SUFDVDtJQUVBLE1BQU1DLGVBQWUsQ0FBQ0M7UUFDcEIsT0FBUUE7WUFDTixLQUFLO2dCQUNILHFCQUFPLDhEQUFDckMseUxBQVVBO29CQUFDc0MsV0FBVTs7Ozs7O1lBQy9CLEtBQUs7Z0JBQ0gscUJBQU8sOERBQUN0Qyx5TEFBVUE7b0JBQUNzQyxXQUFVOzs7Ozs7WUFDL0I7Z0JBQ0UscUJBQU8sOERBQUN0Qyx5TEFBVUE7b0JBQUNzQyxXQUFVOzs7Ozs7UUFDakM7SUFDRjtJQUVBLElBQUl2QixXQUFXO1FBQ2IscUJBQ0UsOERBQUN3QjtZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ2hDLHlMQUFTQTtvQkFBQ2dDLFdBQVU7Ozs7Ozs4QkFDckIsOERBQUNFO29CQUFLRixXQUFVOzhCQUFPOzs7Ozs7Ozs7Ozs7SUFHN0I7SUFFQSxJQUFJLENBQUN6QixlQUFlO1FBQ2xCLHFCQUNFLDhEQUFDMEI7WUFBSUQsV0FBVTs7OEJBQ2IsOERBQUM1Qix5TEFBV0E7b0JBQUM0QixXQUFVOzs7Ozs7OEJBQ3ZCLDhEQUFDRztvQkFBR0gsV0FBVTs4QkFBeUM7Ozs7Ozs4QkFDdkQsOERBQUNJO29CQUFFSixXQUFVOzhCQUFxQjs7Ozs7OzhCQUNsQyw4REFBQ3ZELHlEQUFNQTtvQkFBQzRELFNBQVN0Qjs7c0NBQ2YsOERBQUNmLHlMQUFTQTs0QkFBQ2dDLFdBQVU7Ozs7Ozt3QkFBaUI7Ozs7Ozs7Ozs7Ozs7SUFLOUM7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUQsV0FBVTs7MEJBRWIsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7OzBDQUNDLDhEQUFDSztnQ0FBR04sV0FBVTs7a0RBQ1osOERBQUN2Qyx5TEFBU0E7d0NBQUN1QyxXQUFVOzs7Ozs7a0RBQ3JCLDhEQUFDRTtrREFBSzs7Ozs7Ozs7Ozs7OzBDQUVSLDhEQUFDRTtnQ0FBRUosV0FBVTswQ0FBcUI7Ozs7Ozs7Ozs7OztrQ0FJcEMsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FFYiw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDakMseUxBQVFBO3dDQUFDaUMsV0FBVTs7Ozs7O2tEQUNwQiw4REFBQ087d0NBQ0NDLE9BQU83Qjt3Q0FDUDhCLFVBQVUsQ0FBQ0MsSUFBTTlCLGFBQWE4QixFQUFFQyxNQUFNLENBQUNILEtBQUs7d0NBQzVDUixXQUFVOzswREFFViw4REFBQ1k7Z0RBQU9KLE9BQU07MERBQUs7Ozs7OzswREFDbkIsOERBQUNJO2dEQUFPSixPQUFNOzBEQUFNOzs7Ozs7MERBQ3BCLDhEQUFDSTtnREFBT0osT0FBTTswREFBTTs7Ozs7OzBEQUNwQiw4REFBQ0k7Z0RBQU9KLE9BQU07MERBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQ0FJdkIsOERBQUMvRCx5REFBTUE7Z0NBQUNvRSxTQUFRO2dDQUFVUixTQUFTWjs7a0RBQ2pDLDhEQUFDM0IseUxBQU1BO3dDQUFDa0MsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FHckMsOERBQUN2RCx5REFBTUE7Z0NBQUNvRSxTQUFRO2dDQUFVUixTQUFTZDs7a0RBQ2pDLDhEQUFDMUIseUxBQVFBO3dDQUFDbUMsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FHdkMsOERBQUN2RCx5REFBTUE7Z0NBQUM0RCxTQUFTdEI7O2tEQUNmLDhEQUFDZix5TEFBU0E7d0NBQUNnQyxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU81Qyw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDdEQscURBQUlBOzswQ0FDSCw4REFBQ0csMkRBQVVBO2dDQUFDbUQsV0FBVTs7a0RBQ3BCLDhEQUFDbEQsMERBQVNBO3dDQUFDa0QsV0FBVTtrREFBc0I7Ozs7OztrREFDM0MsOERBQUNyQyx5TEFBTUE7d0NBQUNxQyxXQUFVOzs7Ozs7Ozs7Ozs7MENBRXBCLDhEQUFDckQsNERBQVdBOztrREFDViw4REFBQ3NEO3dDQUFJRCxXQUFXLHNCQUF5RSxPQUFuREosY0FBY3JCLGNBQWN1QyxRQUFRLENBQUNDLFlBQVk7OzRDQUNwRnhDLGNBQWN1QyxRQUFRLENBQUNDLFlBQVk7NENBQUM7Ozs7Ozs7a0RBRXZDLDhEQUFDZDt3Q0FBSUQsV0FBVTs7NENBQ1pGLGFBQWF2QixjQUFjeUMsTUFBTSxDQUFDQyxnQkFBZ0I7MERBQ25ELDhEQUFDZjs7b0RBQUs7b0RBQUUzQixjQUFjdUMsUUFBUSxDQUFDSSxlQUFlO29EQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUtyRCw4REFBQ3hFLHFEQUFJQTs7MENBQ0gsOERBQUNHLDJEQUFVQTtnQ0FBQ21ELFdBQVU7O2tEQUNwQiw4REFBQ2xELDBEQUFTQTt3Q0FBQ2tELFdBQVU7a0RBQXNCOzs7Ozs7a0RBQzNDLDhEQUFDdkMseUxBQVNBO3dDQUFDdUMsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUV2Qiw4REFBQ3JELDREQUFXQTs7a0RBQ1YsOERBQUNzRDt3Q0FBSUQsV0FBVTtrREFBc0J6QixjQUFjdUMsUUFBUSxDQUFDSyxhQUFhOzs7Ozs7a0RBQ3pFLDhEQUFDZjt3Q0FBRUosV0FBVTs7NENBQ1Z6QixjQUFjdUMsUUFBUSxDQUFDTSxjQUFjOzRDQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7O2tDQUs3Qyw4REFBQzFFLHFEQUFJQTs7MENBQ0gsOERBQUNHLDJEQUFVQTtnQ0FBQ21ELFdBQVU7O2tEQUNwQiw4REFBQ2xELDBEQUFTQTt3Q0FBQ2tELFdBQVU7a0RBQXNCOzs7Ozs7a0RBQzNDLDhEQUFDN0IseUxBQUtBO3dDQUFDNkIsV0FBVTs7Ozs7Ozs7Ozs7OzBDQUVuQiw4REFBQ3JELDREQUFXQTs7a0RBQ1YsOERBQUNzRDt3Q0FBSUQsV0FBVTs7NENBQ1pxQixLQUFLQyxLQUFLLENBQUMvQyxjQUFjdUMsUUFBUSxDQUFDUyxTQUFTLEdBQUc7NENBQUk7NENBQUdoRCxjQUFjdUMsUUFBUSxDQUFDUyxTQUFTLEdBQUc7NENBQUc7Ozs7Ozs7a0RBRTlGLDhEQUFDbkI7d0NBQUVKLFdBQVU7OzRDQUNWekIsY0FBY3VDLFFBQVEsQ0FBQ1UsVUFBVTs0Q0FBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLekMsOERBQUM5RSxxREFBSUE7OzBDQUNILDhEQUFDRywyREFBVUE7Z0NBQUNtRCxXQUFVOztrREFDcEIsOERBQUNsRCwwREFBU0E7d0NBQUNrRCxXQUFVO2tEQUFzQjs7Ozs7O2tEQUMzQyw4REFBQy9CLHlMQUFLQTt3Q0FBQytCLFdBQVU7Ozs7Ozs7Ozs7OzswQ0FFbkIsOERBQUNyRCw0REFBV0E7O2tEQUNWLDhEQUFDc0Q7d0NBQUlELFdBQVU7OzRDQUNaekIsY0FBY2tELFdBQVcsQ0FBQ0MsT0FBTyxDQUFDQyxPQUFPOzRDQUFDOzs7Ozs7O2tEQUU3Qyw4REFBQ3ZCO3dDQUFFSixXQUFVOzs0Q0FBZ0M7NENBQ3RDLE1BQU16QixjQUFja0QsV0FBVyxDQUFDQyxPQUFPLENBQUNDLE9BQU87NENBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFPNURwRCxjQUFjVyxRQUFRLENBQUMwQyxNQUFNLEdBQUcsbUJBQy9CLDhEQUFDbEYscURBQUlBO2dCQUFDc0QsV0FBVTswQkFDZCw0RUFBQ3JELDREQUFXQTtvQkFBQ3FELFdBQVU7OEJBQ3JCLDRFQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNwQyx5TEFBS0E7Z0NBQUNvQyxXQUFVOzs7Ozs7MENBQ2pCLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNHO3dDQUFHSCxXQUFVO2tEQUEyQzs7Ozs7O2tEQUd6RCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQ1p6QixjQUFjVyxRQUFRLENBQUMyQyxLQUFLLENBQUMsR0FBRyxHQUFHQyxHQUFHLENBQUMsQ0FBQ0Msd0JBQ3ZDLDhEQUFDOUI7Z0RBQXFCRCxXQUFVOztvREFDN0IrQixRQUFRQyxJQUFJLEtBQUssOEJBQ2hCLDhEQUFDM0QseUxBQVdBO3dEQUFDMkIsV0FBVTs7Ozs7NkVBRXZCLDhEQUFDOUIseUxBQUdBO3dEQUFDOEIsV0FBVTs7Ozs7O2tFQUVqQiw4REFBQ0M7OzBFQUNDLDhEQUFDZ0M7Z0VBQUdqQyxXQUFVOzBFQUE2QitCLFFBQVFHLEtBQUs7Ozs7OzswRUFDeEQsOERBQUM5QjtnRUFBRUosV0FBVTswRUFBeUIrQixRQUFRSSxXQUFXOzs7Ozs7Ozs7Ozs7OytDQVJuREosUUFBUUssRUFBRTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBb0JsQyw4REFBQ3JGLHFEQUFJQTtnQkFBQ3NGLGNBQWE7Z0JBQVdyQyxXQUFVOztrQ0FDdEMsOERBQUMvQyx5REFBUUE7d0JBQUMrQyxXQUFVOzswQ0FDbEIsOERBQUM5Qyw0REFBV0E7Z0NBQUNzRCxPQUFNOzBDQUFXOzs7Ozs7MENBQzlCLDhEQUFDdEQsNERBQVdBO2dDQUFDc0QsT0FBTTswQ0FBYzs7Ozs7OzBDQUNqQyw4REFBQ3RELDREQUFXQTtnQ0FBQ3NELE9BQU07MENBQVc7Ozs7OzswQ0FDOUIsOERBQUN0RCw0REFBV0E7Z0NBQUNzRCxPQUFNOzBDQUFhOzs7Ozs7MENBQ2hDLDhEQUFDdEQsNERBQVdBO2dDQUFDc0QsT0FBTTswQ0FBUTs7Ozs7Ozs7Ozs7O2tDQUc3Qiw4REFBQ3hELDREQUFXQTt3QkFBQ3dELE9BQU07d0JBQVdSLFdBQVU7a0NBQ3RDLDRFQUFDNUMsMEZBQW1CQTs0QkFBQzRCLE1BQU1UOzs7Ozs7Ozs7OztrQ0FHN0IsOERBQUN2Qiw0REFBV0E7d0JBQUN3RCxPQUFNO3dCQUFjUixXQUFVO2tDQUN6Qyw0RUFBQzNDLHNGQUFpQkE7NEJBQUMyQixNQUFNVCxjQUFjYSxXQUFXOzRCQUFFVCxXQUFXQTs7Ozs7Ozs7Ozs7a0NBR2pFLDhEQUFDM0IsNERBQVdBO3dCQUFDd0QsT0FBTTt3QkFBV1IsV0FBVTtrQ0FDdEMsNEVBQUMxQyw4RUFBYUE7NEJBQUM0QixVQUFVWCxjQUFjVyxRQUFROzRCQUFFOEIsUUFBUXpDLGNBQWN5QyxNQUFNOzs7Ozs7Ozs7OztrQ0FHL0UsOERBQUNoRSw0REFBV0E7d0JBQUN3RCxPQUFNO3dCQUFhUixXQUFVO2tDQUN4Qyw0RUFBQ3pDLDBGQUFtQkE7NEJBQ2xCK0UsV0FBVy9ELGNBQWN1QyxRQUFRLENBQUNDLFlBQVk7NEJBQzlDVSxhQUFhbEQsY0FBY2tELFdBQVc7NEJBQ3RDYyxtQkFBbUJoRSxjQUFjYSxXQUFXLENBQUNtRCxpQkFBaUI7Ozs7Ozs7Ozs7O2tDQUlsRSw4REFBQ3ZGLDREQUFXQTt3QkFBQ3dELE9BQU07d0JBQVFSLFdBQVU7a0NBQ25DLDRFQUFDeEMsNkVBQVlBOzRCQUFDZ0YsT0FBT2pFLGNBQWNpRSxLQUFLOzs7Ozs7Ozs7Ozs7Ozs7OzswQkFLNUMsOERBQUM5RixxREFBSUE7O2tDQUNILDhEQUFDRywyREFBVUE7OzBDQUNULDhEQUFDQywwREFBU0E7Z0NBQUNrRCxXQUFVOztrREFDbkIsOERBQUM5Qix5TEFBR0E7d0NBQUM4QixXQUFVOzs7Ozs7a0RBQ2YsOERBQUNFO2tEQUFLOzs7Ozs7Ozs7Ozs7MENBRVIsOERBQUN0RCxnRUFBZUE7MENBQUM7Ozs7Ozs7Ozs7OztrQ0FJbkIsOERBQUNELDREQUFXQTtrQ0FDViw0RUFBQ3NEOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDaUM7NENBQUdqQyxXQUFVOztnREFBbUI7Z0RBQVV6QixjQUFjeUMsTUFBTSxDQUFDeUIsZUFBZTs7Ozs7OztzREFDL0UsOERBQUNyQzs0Q0FBRUosV0FBVTtzREFBNkI7Ozs7OztzREFHMUMsOERBQUN2RCx5REFBTUE7NENBQUNpRyxNQUFLOzRDQUFLN0IsU0FBUTtzREFBVTs7Ozs7Ozs7Ozs7OzhDQUV0Qyw4REFBQ1o7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDaUM7NENBQUdqQyxXQUFVO3NEQUFtQjs7Ozs7O3NEQUNqQyw4REFBQ0k7NENBQUVKLFdBQVU7O2dEQUE2QjtnREFDekJ6QixjQUFjdUMsUUFBUSxDQUFDVSxVQUFVO2dEQUFDOzs7Ozs7O3NEQUVuRCw4REFBQy9FLHlEQUFNQTs0Q0FBQ2lHLE1BQUs7NENBQUs3QixTQUFRO3NEQUFVOzs7Ozs7Ozs7Ozs7OENBRXRDLDhEQUFDWjtvQ0FBSUQsV0FBVTs7c0RBQ2IsOERBQUNpQzs0Q0FBR2pDLFdBQVU7c0RBQW1COzs7Ozs7c0RBQ2pDLDhEQUFDSTs0Q0FBRUosV0FBVTtzREFBNkI7Ozs7OztzREFHMUMsOERBQUN2RCx5REFBTUE7NENBQUNpRyxNQUFLOzRDQUFLN0IsU0FBUTtzREFBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPbEQ7R0ExU3dCdkM7S0FBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2FwcC9kYXNoYm9hcmQvYW5hbHl0aWNzL3BhZ2UudHN4PzYwYTciXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2JhZGdlJ1xuaW1wb3J0IHsgVGFicywgVGFic0NvbnRlbnQsIFRhYnNMaXN0LCBUYWJzVHJpZ2dlciB9IGZyb20gJ0AvY29tcG9uZW50cy91aS90YWJzJ1xuaW1wb3J0IHsgYW5hbHl0aWNzU2VydmljZSwgQW5hbHl0aWNzRGF0YSB9IGZyb20gJ0Avc2VydmljZXMvYW5hbHl0aWNzU2VydmljZSdcbmltcG9ydCB7IFBlcmZvcm1hbmNlT3ZlcnZpZXcgfSBmcm9tICdAL2NvbXBvbmVudHMvYW5hbHl0aWNzL1BlcmZvcm1hbmNlT3ZlcnZpZXcnXG5pbXBvcnQgeyBQZXJmb3JtYW5jZUNoYXJ0cyB9IGZyb20gJ0AvY29tcG9uZW50cy9hbmFseXRpY3MvUGVyZm9ybWFuY2VDaGFydHMnXG5pbXBvcnQgeyBJbnNpZ2h0c1BhbmVsIH0gZnJvbSAnQC9jb21wb25lbnRzL2FuYWx5dGljcy9JbnNpZ2h0c1BhbmVsJ1xuaW1wb3J0IHsgQmVuY2htYXJrQ29tcGFyaXNvbiB9IGZyb20gJ0AvY29tcG9uZW50cy9hbmFseXRpY3MvQmVuY2htYXJrQ29tcGFyaXNvbidcbmltcG9ydCB7IEdvYWxzVHJhY2tlciB9IGZyb20gJ0AvY29tcG9uZW50cy9hbmFseXRpY3MvR29hbHNUcmFja2VyJ1xuaW1wb3J0IHtcbiAgQmFyQ2hhcnQzLFxuICBUcmVuZGluZ1VwLFxuICBUYXJnZXQsXG4gIEJyYWluLFxuICBEb3dubG9hZCxcbiAgU2hhcmUyLFxuICBDYWxlbmRhcixcbiAgRmlsdGVyLFxuICBSZWZyZXNoQ3csXG4gIEF3YXJkLFxuICBVc2VycyxcbiAgWmFwLFxuICBDbG9jayxcbiAgU3RhcixcbiAgQWxlcnRDaXJjbGUsXG4gIENoZWNrQ2lyY2xlXG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQW5hbHl0aWNzUGFnZSgpIHtcbiAgY29uc3QgW2FuYWx5dGljc0RhdGEsIHNldEFuYWx5dGljc0RhdGFdID0gdXNlU3RhdGU8QW5hbHl0aWNzRGF0YSB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbdGltZVJhbmdlLCBzZXRUaW1lUmFuZ2VdID0gdXNlU3RhdGU8JzdkJyB8ICczMGQnIHwgJzkwZCcgfCAnMXknPignMzBkJylcbiAgY29uc3QgW3NlbGVjdGVkQ2F0ZWdvcnksIHNldFNlbGVjdGVkQ2F0ZWdvcnldID0gdXNlU3RhdGU8c3RyaW5nPignYWxsJylcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGxvYWRBbmFseXRpY3NEYXRhKClcbiAgfSwgW3RpbWVSYW5nZV0pXG5cbiAgY29uc3QgbG9hZEFuYWx5dGljc0RhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIHNldElzTG9hZGluZyh0cnVlKVxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IGFuYWx5dGljc1NlcnZpY2UuZ2V0QW5hbHl0aWNzRGF0YSgndXNlci0xMjMnLCB0aW1lUmFuZ2UpXG4gICAgICBcbiAgICAgIC8vIEdlbmVyYXRlIEFJIGluc2lnaHRzXG4gICAgICBjb25zdCBpbnNpZ2h0cyA9IGF3YWl0IGFuYWx5dGljc1NlcnZpY2UuZ2VuZXJhdGVJbnNpZ2h0cyhkYXRhLnBlcmZvcm1hbmNlKVxuICAgICAgZGF0YS5pbnNpZ2h0cyA9IGluc2lnaHRzXG4gICAgICBcbiAgICAgIHNldEFuYWx5dGljc0RhdGEoZGF0YSlcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyBhbmFseXRpY3MgZGF0YTonLCBlcnJvcilcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGV4cG9ydFJlcG9ydCA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnRXhwb3J0aW5nIGFuYWx5dGljcyByZXBvcnQuLi4nKVxuICAgIC8vIEltcGxlbWVudCBQREYgZXhwb3J0IGZ1bmN0aW9uYWxpdHlcbiAgfVxuXG4gIGNvbnN0IHNoYXJlUmVwb3J0ID0gKCkgPT4ge1xuICAgIGNvbnNvbGUubG9nKCdTaGFyaW5nIGFuYWx5dGljcyByZXBvcnQuLi4nKVxuICAgIC8vIEltcGxlbWVudCBzaGFyaW5nIGZ1bmN0aW9uYWxpdHlcbiAgfVxuXG4gIGNvbnN0IGdldFRpbWVSYW5nZUxhYmVsID0gKHJhbmdlOiBzdHJpbmcpID0+IHtcbiAgICBzd2l0Y2ggKHJhbmdlKSB7XG4gICAgICBjYXNlICc3ZCc6IHJldHVybiAnTGFzdCA3IERheXMnXG4gICAgICBjYXNlICczMGQnOiByZXR1cm4gJ0xhc3QgMzAgRGF5cydcbiAgICAgIGNhc2UgJzkwZCc6IHJldHVybiAnTGFzdCAzIE1vbnRocydcbiAgICAgIGNhc2UgJzF5JzogcmV0dXJuICdMYXN0IFllYXInXG4gICAgICBkZWZhdWx0OiByZXR1cm4gJ0xhc3QgMzAgRGF5cydcbiAgICB9XG4gIH1cblxuICBjb25zdCBnZXRTY29yZUNvbG9yID0gKHNjb3JlOiBudW1iZXIpID0+IHtcbiAgICBpZiAoc2NvcmUgPj0gODApIHJldHVybiAndGV4dC1ncmVlbi02MDAnXG4gICAgaWYgKHNjb3JlID49IDcwKSByZXR1cm4gJ3RleHQteWVsbG93LTYwMCdcbiAgICByZXR1cm4gJ3RleHQtcmVkLTYwMCdcbiAgfVxuXG4gIGNvbnN0IGdldFRyZW5kSWNvbiA9ICh0cmVuZDogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoICh0cmVuZCkge1xuICAgICAgY2FzZSAnaW1wcm92aW5nJzpcbiAgICAgICAgcmV0dXJuIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgY2FzZSAnZGVjbGluaW5nJzpcbiAgICAgICAgcmV0dXJuIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNjAwIHJvdGF0ZS0xODBcIiAvPlxuICAgICAgZGVmYXVsdDpcbiAgICAgICAgcmV0dXJuIDxUcmVuZGluZ1VwIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1ncmF5LTYwMFwiIC8+XG4gICAgfVxuICB9XG5cbiAgaWYgKGlzTG9hZGluZykge1xuICAgIHJldHVybiAoXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIG1pbi1oLXNjcmVlblwiPlxuICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cImgtOCB3LTggYW5pbWF0ZS1zcGluIHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC0yXCI+TG9hZGluZyBhbmFseXRpY3MuLi48L3NwYW4+XG4gICAgICA8L2Rpdj5cbiAgICApXG4gIH1cblxuICBpZiAoIWFuYWx5dGljc0RhdGEpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC0xNiB3LTE2IHRleHQtZ3JheS00MDAgbXgtYXV0byBtYi00XCIgLz5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1tZWRpdW0gdGV4dC1ncmF5LTYwMCBtYi0yXCI+Tm8gQW5hbHl0aWNzIERhdGE8L2gzPlxuICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LWdyYXktNTAwIG1iLTRcIj5VbmFibGUgdG8gbG9hZCBhbmFseXRpY3MgZGF0YS48L3A+XG4gICAgICAgIDxCdXR0b24gb25DbGljaz17bG9hZEFuYWx5dGljc0RhdGF9PlxuICAgICAgICAgIDxSZWZyZXNoQ3cgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cbiAgICAgICAgICBSZXRyeVxuICAgICAgICA8L0J1dHRvbj5cbiAgICAgIDwvZGl2PlxuICAgIClcbiAgfVxuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LThcIj5cbiAgICAgIHsvKiBIZWFkZXIgKi99XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICA8ZGl2PlxuICAgICAgICAgIDxoMSBjbGFzc05hbWU9XCJ0ZXh0LTN4bCBmb250LWJvbGQgdGV4dC1ncmF5LTYwMCBmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxCYXJDaGFydDMgY2xhc3NOYW1lPVwiaC04IHctOCB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICAgICAgICAgIDxzcGFuPlBlcmZvcm1hbmNlIEFuYWx5dGljczwvc3Bhbj5cbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgbXQtMlwiPlxuICAgICAgICAgICAgQ29tcHJlaGVuc2l2ZSBpbnNpZ2h0cyBpbnRvIHlvdXIgaW50ZXJ2aWV3IHBlcmZvcm1hbmNlIGFuZCBwcm9ncmVzc1xuICAgICAgICAgIDwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC00XCI+XG4gICAgICAgICAgey8qIFRpbWUgUmFuZ2UgU2VsZWN0b3IgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxDYWxlbmRhciBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JheS02MDBcIiAvPlxuICAgICAgICAgICAgPHNlbGVjdFxuICAgICAgICAgICAgICB2YWx1ZT17dGltZVJhbmdlfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IHNldFRpbWVSYW5nZShlLnRhcmdldC52YWx1ZSBhcyBhbnkpfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJweC0zIHB5LTIgYm9yZGVyIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkLW1kIGZvY3VzOm91dGxpbmUtbm9uZSBmb2N1czpyaW5nLTIgZm9jdXM6cmluZy1ibHVlLTUwMFwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCI3ZFwiPkxhc3QgNyBEYXlzPC9vcHRpb24+XG4gICAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9XCIzMGRcIj5MYXN0IDMwIERheXM8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjkwZFwiPkxhc3QgMyBNb250aHM8L29wdGlvbj5cbiAgICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT1cIjF5XCI+TGFzdCBZZWFyPC9vcHRpb24+XG4gICAgICAgICAgICA8L3NlbGVjdD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICBcbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17c2hhcmVSZXBvcnR9PlxuICAgICAgICAgICAgPFNoYXJlMiBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgU2hhcmVcbiAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJvdXRsaW5lXCIgb25DbGljaz17ZXhwb3J0UmVwb3J0fT5cbiAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgRXhwb3J0XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgPEJ1dHRvbiBvbkNsaWNrPXtsb2FkQW5hbHl0aWNzRGF0YX0+XG4gICAgICAgICAgICA8UmVmcmVzaEN3IGNsYXNzTmFtZT1cIm1yLTIgaC00IHctNFwiIC8+XG4gICAgICAgICAgICBSZWZyZXNoXG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBLZXkgTWV0cmljcyBPdmVydmlldyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNlwiPlxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTJcIj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPkF2ZXJhZ2UgU2NvcmU8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgIDxUYXJnZXQgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YHRleHQtMnhsIGZvbnQtYm9sZCAke2dldFNjb3JlQ29sb3IoYW5hbHl0aWNzRGF0YS5vdmVydmlldy5hdmVyYWdlU2NvcmUpfWB9PlxuICAgICAgICAgICAgICB7YW5hbHl0aWNzRGF0YS5vdmVydmlldy5hdmVyYWdlU2NvcmV9JVxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMSB0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICB7Z2V0VHJlbmRJY29uKGFuYWx5dGljc0RhdGEudHJlbmRzLnBlcmZvcm1hbmNlVHJlbmQpfVxuICAgICAgICAgICAgICA8c3Bhbj4re2FuYWx5dGljc0RhdGEub3ZlcnZpZXcuaW1wcm92ZW1lbnRSYXRlfSUgZnJvbSBsYXN0IHBlcmlvZDwvc3Bhbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTJcIj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPlRvdGFsIFNlc3Npb25zPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8QmFyQ2hhcnQzIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57YW5hbHl0aWNzRGF0YS5vdmVydmlldy50b3RhbFNlc3Npb25zfTwvZGl2PlxuICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAge2FuYWx5dGljc0RhdGEub3ZlcnZpZXcuY29tcGxldGlvblJhdGV9JSBjb21wbGV0aW9uIHJhdGVcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5UaW1lIFByYWN0aWNlZDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCIgLz5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj5cbiAgICAgICAgICAgICAge01hdGguZmxvb3IoYW5hbHl0aWNzRGF0YS5vdmVydmlldy50aW1lU3BlbnQgLyA2MCl9aCB7YW5hbHl0aWNzRGF0YS5vdmVydmlldy50aW1lU3BlbnQgJSA2MH1tXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgIHthbmFseXRpY3NEYXRhLm92ZXJ2aWV3LnN0cmVha0RheXN9IGRheSBzdHJlYWtcbiAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj5JbmR1c3RyeSBSYW5rPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICA8QXdhcmQgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZCB0ZXh0LWJsdWUtNjAwXCI+XG4gICAgICAgICAgICAgIHthbmFseXRpY3NEYXRhLmNvbXBhcmlzb25zLnJhbmtpbmcub3ZlcmFsbH0lXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgIFRvcCB7MTAwIC0gYW5hbHl0aWNzRGF0YS5jb21wYXJpc29ucy5yYW5raW5nLm92ZXJhbGx9JSBvZiBjYW5kaWRhdGVzXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgPC9DYXJkPlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBBSSBJbnNpZ2h0cyBCYW5uZXIgKi99XG4gICAgICB7YW5hbHl0aWNzRGF0YS5pbnNpZ2h0cy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiYm9yZGVyLWJsdWUtMjAwIGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTUwIHRvLXB1cnBsZS01MFwiPlxuICAgICAgICAgIDxDYXJkQ29udGVudCBjbGFzc05hbWU9XCJwdC02XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC00XCI+XG4gICAgICAgICAgICAgIDxCcmFpbiBjbGFzc05hbWU9XCJoLTggdy04IHRleHQtYmx1ZS02MDAgbXQtMVwiIC8+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgPGgzIGNsYXNzTmFtZT1cInRleHQtbGcgZm9udC1zZW1pYm9sZCB0ZXh0LWJsdWUtOTAwIG1iLTJcIj5cbiAgICAgICAgICAgICAgICAgIEFJLVBvd2VyZWQgSW5zaWdodHNcbiAgICAgICAgICAgICAgICA8L2gzPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBtZDpncmlkLWNvbHMtMiBnYXAtNFwiPlxuICAgICAgICAgICAgICAgICAge2FuYWx5dGljc0RhdGEuaW5zaWdodHMuc2xpY2UoMCwgMikubWFwKChpbnNpZ2h0KSA9PiAoXG4gICAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbnNpZ2h0LmlkfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLXN0YXJ0IHNwYWNlLXgtM1wiPlxuICAgICAgICAgICAgICAgICAgICAgIHtpbnNpZ2h0LnR5cGUgPT09ICdhY2hpZXZlbWVudCcgPyAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8Q2hlY2tDaXJjbGUgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LWdyZWVuLTYwMCBtdC0wLjVcIiAvPlxuICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICA8WmFwIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC15ZWxsb3ctNjAwIG10LTAuNVwiIC8+XG4gICAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIHRleHQtYmx1ZS05MDBcIj57aW5zaWdodC50aXRsZX08L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWJsdWUtNzAwXCI+e2luc2lnaHQuZGVzY3JpcHRpb259PC9wPlxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgICl9XG5cbiAgICAgIHsvKiBNYWluIEFuYWx5dGljcyBUYWJzICovfVxuICAgICAgPFRhYnMgZGVmYXVsdFZhbHVlPVwib3ZlcnZpZXdcIiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgPFRhYnNMaXN0IGNsYXNzTmFtZT1cImdyaWQgdy1mdWxsIGdyaWQtY29scy01XCI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwib3ZlcnZpZXdcIj5PdmVydmlldzwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwicGVyZm9ybWFuY2VcIj5QZXJmb3JtYW5jZTwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiaW5zaWdodHNcIj5BSSBJbnNpZ2h0czwvVGFic1RyaWdnZXI+XG4gICAgICAgICAgPFRhYnNUcmlnZ2VyIHZhbHVlPVwiYmVuY2htYXJrc1wiPkJlbmNobWFya3M8L1RhYnNUcmlnZ2VyPlxuICAgICAgICAgIDxUYWJzVHJpZ2dlciB2YWx1ZT1cImdvYWxzXCI+R29hbHM8L1RhYnNUcmlnZ2VyPlxuICAgICAgICA8L1RhYnNMaXN0PlxuXG4gICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cIm92ZXJ2aWV3XCIgY2xhc3NOYW1lPVwic3BhY2UteS02XCI+XG4gICAgICAgICAgPFBlcmZvcm1hbmNlT3ZlcnZpZXcgZGF0YT17YW5hbHl0aWNzRGF0YX0gLz5cbiAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJwZXJmb3JtYW5jZVwiIGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIDxQZXJmb3JtYW5jZUNoYXJ0cyBkYXRhPXthbmFseXRpY3NEYXRhLnBlcmZvcm1hbmNlfSB0aW1lUmFuZ2U9e3RpbWVSYW5nZX0gLz5cbiAgICAgICAgPC9UYWJzQ29udGVudD5cblxuICAgICAgICA8VGFic0NvbnRlbnQgdmFsdWU9XCJpbnNpZ2h0c1wiIGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICAgIDxJbnNpZ2h0c1BhbmVsIGluc2lnaHRzPXthbmFseXRpY3NEYXRhLmluc2lnaHRzfSB0cmVuZHM9e2FuYWx5dGljc0RhdGEudHJlbmRzfSAvPlxuICAgICAgICA8L1RhYnNDb250ZW50PlxuXG4gICAgICAgIDxUYWJzQ29udGVudCB2YWx1ZT1cImJlbmNobWFya3NcIiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICA8QmVuY2htYXJrQ29tcGFyaXNvbiBcbiAgICAgICAgICAgIHVzZXJTY29yZT17YW5hbHl0aWNzRGF0YS5vdmVydmlldy5hdmVyYWdlU2NvcmV9XG4gICAgICAgICAgICBjb21wYXJpc29ucz17YW5hbHl0aWNzRGF0YS5jb21wYXJpc29uc31cbiAgICAgICAgICAgIGNhdGVnb3J5QnJlYWtkb3duPXthbmFseXRpY3NEYXRhLnBlcmZvcm1hbmNlLmNhdGVnb3J5QnJlYWtkb3dufVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvVGFic0NvbnRlbnQ+XG5cbiAgICAgICAgPFRhYnNDb250ZW50IHZhbHVlPVwiZ29hbHNcIiBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgICAgICA8R29hbHNUcmFja2VyIGdvYWxzPXthbmFseXRpY3NEYXRhLmdvYWxzfSAvPlxuICAgICAgICA8L1RhYnNDb250ZW50PlxuICAgICAgPC9UYWJzPlxuXG4gICAgICB7LyogUXVpY2sgQWN0aW9ucyAqL31cbiAgICAgIDxDYXJkPlxuICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPFphcCBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQteWVsbG93LTYwMFwiIC8+XG4gICAgICAgICAgICA8c3Bhbj5SZWNvbW1lbmRlZCBBY3Rpb25zPC9zcGFuPlxuICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+XG4gICAgICAgICAgICBCYXNlZCBvbiB5b3VyIHBlcmZvcm1hbmNlIGFuYWx5c2lzLCBoZXJlIGFyZSBzb21lIHJlY29tbWVuZGVkIG5leHQgc3RlcHNcbiAgICAgICAgICA8L0NhcmREZXNjcmlwdGlvbj5cbiAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC00XCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gbWItMlwiPlByYWN0aWNlIHthbmFseXRpY3NEYXRhLnRyZW5kcy53ZWFrZXN0Q2F0ZWdvcnl9PC9oND5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICBGb2N1cyBvbiB5b3VyIHdlYWtlc3QgYXJlYSB0byBzZWUgdGhlIGJpZ2dlc3QgaW1wcm92ZW1lbnRcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8QnV0dG9uIHNpemU9XCJzbVwiIHZhcmlhbnQ9XCJvdXRsaW5lXCI+U3RhcnQgUHJhY3RpY2U8L0J1dHRvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTQgYm9yZGVyIHJvdW5kZWQtbGdcIj5cbiAgICAgICAgICAgICAgPGg0IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtIG1iLTJcIj5TY2hlZHVsZSBSZWd1bGFyIFNlc3Npb25zPC9oND5cbiAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNjAwIG1iLTNcIj5cbiAgICAgICAgICAgICAgICBNYWludGFpbiB5b3VyIHthbmFseXRpY3NEYXRhLm92ZXJ2aWV3LnN0cmVha0RheXN9LWRheSBzdHJlYWsgd2l0aCBjb25zaXN0ZW50IHByYWN0aWNlXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwic21cIiB2YXJpYW50PVwib3V0bGluZVwiPlNldCBTY2hlZHVsZTwvQnV0dG9uPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICA8aDQgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW0gbWItMlwiPkpvaW4gU3R1ZHkgR3JvdXA8L2g0PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItM1wiPlxuICAgICAgICAgICAgICAgIENvbm5lY3Qgd2l0aCBwZWVycyBpbiB5b3VyIGluZHVzdHJ5IGZvciBjb2xsYWJvcmF0aXZlIGxlYXJuaW5nXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPEJ1dHRvbiBzaXplPVwic21cIiB2YXJpYW50PVwib3V0bGluZVwiPkZpbmQgR3JvdXBzPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgIDwvQ2FyZD5cbiAgICA8L2Rpdj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwiQnV0dG9uIiwiQ2FyZCIsIkNhcmRDb250ZW50IiwiQ2FyZERlc2NyaXB0aW9uIiwiQ2FyZEhlYWRlciIsIkNhcmRUaXRsZSIsIlRhYnMiLCJUYWJzQ29udGVudCIsIlRhYnNMaXN0IiwiVGFic1RyaWdnZXIiLCJhbmFseXRpY3NTZXJ2aWNlIiwiUGVyZm9ybWFuY2VPdmVydmlldyIsIlBlcmZvcm1hbmNlQ2hhcnRzIiwiSW5zaWdodHNQYW5lbCIsIkJlbmNobWFya0NvbXBhcmlzb24iLCJHb2Fsc1RyYWNrZXIiLCJCYXJDaGFydDMiLCJUcmVuZGluZ1VwIiwiVGFyZ2V0IiwiQnJhaW4iLCJEb3dubG9hZCIsIlNoYXJlMiIsIkNhbGVuZGFyIiwiUmVmcmVzaEN3IiwiQXdhcmQiLCJaYXAiLCJDbG9jayIsIkFsZXJ0Q2lyY2xlIiwiQ2hlY2tDaXJjbGUiLCJBbmFseXRpY3NQYWdlIiwiYW5hbHl0aWNzRGF0YSIsInNldEFuYWx5dGljc0RhdGEiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ0aW1lUmFuZ2UiLCJzZXRUaW1lUmFuZ2UiLCJzZWxlY3RlZENhdGVnb3J5Iiwic2V0U2VsZWN0ZWRDYXRlZ29yeSIsImxvYWRBbmFseXRpY3NEYXRhIiwiZGF0YSIsImdldEFuYWx5dGljc0RhdGEiLCJpbnNpZ2h0cyIsImdlbmVyYXRlSW5zaWdodHMiLCJwZXJmb3JtYW5jZSIsImVycm9yIiwiY29uc29sZSIsImV4cG9ydFJlcG9ydCIsImxvZyIsInNoYXJlUmVwb3J0IiwiZ2V0VGltZVJhbmdlTGFiZWwiLCJyYW5nZSIsImdldFNjb3JlQ29sb3IiLCJzY29yZSIsImdldFRyZW5kSWNvbiIsInRyZW5kIiwiY2xhc3NOYW1lIiwiZGl2Iiwic3BhbiIsImgzIiwicCIsIm9uQ2xpY2siLCJoMSIsInNlbGVjdCIsInZhbHVlIiwib25DaGFuZ2UiLCJlIiwidGFyZ2V0Iiwib3B0aW9uIiwidmFyaWFudCIsIm92ZXJ2aWV3IiwiYXZlcmFnZVNjb3JlIiwidHJlbmRzIiwicGVyZm9ybWFuY2VUcmVuZCIsImltcHJvdmVtZW50UmF0ZSIsInRvdGFsU2Vzc2lvbnMiLCJjb21wbGV0aW9uUmF0ZSIsIk1hdGgiLCJmbG9vciIsInRpbWVTcGVudCIsInN0cmVha0RheXMiLCJjb21wYXJpc29ucyIsInJhbmtpbmciLCJvdmVyYWxsIiwibGVuZ3RoIiwic2xpY2UiLCJtYXAiLCJpbnNpZ2h0IiwidHlwZSIsImg0IiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImlkIiwiZGVmYXVsdFZhbHVlIiwidXNlclNjb3JlIiwiY2F0ZWdvcnlCcmVha2Rvd24iLCJnb2FscyIsIndlYWtlc3RDYXRlZ29yeSIsInNpemUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/analytics/page.tsx\n"));

/***/ })

});