/**
 * @license lucide-react v0.312.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const FileCog = createLucideIcon("FileCog", [
  ["path", { d: "M4 22h14a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v2", key: "17k7jt" }],
  ["path", { d: "M14 2v4a2 2 0 0 0 2 2h4", key: "tnqrlb" }],
  ["circle", { cx: "6", cy: "14", r: "3", key: "a1xfv6" }],
  ["path", { d: "M6 10v1", key: "xs0f9j" }],
  ["path", { d: "M6 17v1", key: "idyhc0" }],
  ["path", { d: "M10 14H9", key: "m5fm2q" }],
  ["path", { d: "M3 14H2", key: "19ot09" }],
  ["path", { d: "m9 11-.88.88", key: "lhul2b" }],
  ["path", { d: "M3.88 16.12 3 17", key: "169z9n" }],
  ["path", { d: "m9 17-.88-.88", key: "5io96w" }],
  ["path", { d: "M3.88 11.88 3 11", key: "1ynhy1" }]
]);

export { FileCog as default };
//# sourceMappingURL=file-cog.js.map
