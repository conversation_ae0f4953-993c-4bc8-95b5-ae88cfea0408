"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/coaching/ai/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/coaching/ai/page.tsx":
/*!************************************************!*\
  !*** ./src/app/dashboard/coaching/ai/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AICoachingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/aiCoachingClient */ \"(app-pages-browser)/./src/services/aiCoachingClient.ts\");\n/* harmony import */ var _services_emotionalIntelligenceService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/emotionalIntelligenceService */ \"(app-pages-browser)/./src/services/emotionalIntelligenceService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AICoachingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const role = searchParams.get(\"role\") || \"software-engineer\";\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentResponse, setCurrentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [questionCount, setQuestionCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [emotionalState, setEmotionalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        confidence: 75,\n        stress: 30,\n        engagement: 80,\n        clarity: 70\n    });\n    const [sessionConfig, setSessionConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        sessionType: \"practice\",\n        difficulty: 5,\n        focusAreas: [\n            \"System Design\",\n            \"Algorithms\"\n        ],\n        timeLimit: 3600,\n        questionCount: 10,\n        adaptiveDifficulty: true,\n        emotionalAnalysis: true\n    });\n    const responseTextareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mediaStreamRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeSession();\n        return ()=>{\n            // Cleanup on unmount\n            if (mediaStreamRef.current) {\n                mediaStreamRef.current.getTracks().forEach((track)=>track.stop());\n            }\n        };\n    }, [\n        role\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            // Update progress once when session is created\n            updateProgress();\n            // Set up periodic updates only if session is active\n            if (session.status === \"active\") {\n                const interval = setInterval(()=>{\n                    updateProgress();\n                }, 5000) // Update every 5 seconds instead of 1 second\n                ;\n                return ()=>clearInterval(interval);\n            }\n        }\n    }, [\n        session === null || session === void 0 ? void 0 : session.id,\n        session === null || session === void 0 ? void 0 : session.status\n    ]);\n    const initializeSession = async ()=>{\n        try {\n            setIsLoading(true);\n            // Create a simple demo session without API calls for now\n            const demoSession = {\n                id: \"demo-session-\".concat(Date.now()),\n                role,\n                status: \"active\",\n                startTime: new Date(),\n                config: sessionConfig,\n                userProfile: {\n                    level: \"intermediate\",\n                    experience: [\n                        \"JavaScript\",\n                        \"React\",\n                        \"Node.js\"\n                    ],\n                    weaknesses: [\n                        \"System Design\",\n                        \"Algorithms\"\n                    ],\n                    goals: [\n                        \"Pass technical interviews\",\n                        \"Improve problem-solving skills\"\n                    ]\n                }\n            };\n            setSession(demoSession);\n            // Generate first demo question\n            const demoQuestions = {\n                \"software-engineer\": \"Can you walk me through how you would design a URL shortening service like bit.ly? Consider the system architecture, database design, and scalability requirements.\",\n                \"product-manager\": \"How would you prioritize features for a new mobile app with limited engineering resources? Walk me through your decision-making process.\",\n                \"data-scientist\": \"Describe how you would approach building a recommendation system for an e-commerce platform. What data would you need and what algorithms would you consider?\"\n            };\n            setCurrentQuestion(demoQuestions[role] || demoQuestions[\"software-engineer\"]);\n            setQuestionCount(1);\n            // Set initial progress\n            setProgress({\n                currentQuestionIndex: 1,\n                totalQuestions: 10,\n                timeElapsed: 0,\n                completionPercentage: 10,\n                currentDifficulty: sessionConfig.difficulty,\n                averageScore: 0,\n                recentPerformance: []\n            });\n            // Start media capture for emotional analysis\n            if (sessionConfig.emotionalAnalysis) {\n                await startMediaCapture();\n            }\n        } catch (error) {\n            console.error(\"Error initializing session:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateProgress = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            try {\n                const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.getSessionProgress(session.id);\n                if (result.success && result.progress) {\n                    setProgress(result.progress);\n                } else {\n                    console.warn(\"Failed to get session progress:\", result.error);\n                }\n            } catch (error) {\n                console.error(\"Error updating progress:\", error);\n            }\n        }\n    };\n    const startMediaCapture = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true,\n                audio: true\n            });\n            mediaStreamRef.current = stream;\n            setIsRecording(true);\n            // Start periodic emotional analysis\n            startEmotionalAnalysis();\n        } catch (error) {\n            console.error(\"Error accessing media devices:\", error);\n        }\n    };\n    const startEmotionalAnalysis = ()=>{\n        setInterval(async ()=>{\n            if (!session || !mediaStreamRef.current) return;\n            try {\n                // Capture audio/video snippets for analysis\n                const audioBlob = await captureAudioSnippet();\n                const videoBlob = await captureVideoSnippet();\n                // Process emotional data\n                const analysis = await _services_emotionalIntelligenceService__WEBPACK_IMPORTED_MODULE_9__.emotionalIntelligenceService.processRealTimeEmotionalData(session.id, audioBlob, videoBlob);\n                // Update emotional state\n                const newEmotionalState = {\n                    confidence: Math.round(analysis.overallState.confidence * 100),\n                    stress: Math.round(analysis.overallState.stress * 100),\n                    engagement: Math.round(analysis.overallState.engagement * 100),\n                    clarity: Math.round(analysis.overallState.clarity * 100)\n                };\n                setEmotionalState(newEmotionalState);\n                // Update session with emotional data\n                await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.updateEmotionalState(session.id, {\n                    confidence: analysis.overallState.confidence,\n                    stress: analysis.overallState.stress,\n                    engagement: analysis.overallState.engagement,\n                    clarity: analysis.overallState.clarity\n                });\n            } catch (error) {\n                console.error(\"Error in emotional analysis:\", error);\n            }\n        }, 5000) // Analyze every 5 seconds\n        ;\n    };\n    const submitResponse = async ()=>{\n        if (!(session === null || session === void 0 ? void 0 : session.id) || !currentResponse.trim()) return;\n        try {\n            var _session_currentQuestion;\n            setIsLoading(true);\n            const responseTime = Date.now() - (((_session_currentQuestion = session.currentQuestion) === null || _session_currentQuestion === void 0 ? void 0 : _session_currentQuestion.timestamp) || Date.now());\n            const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.processResponse({\n                sessionId: session.id,\n                userResponse: currentResponse,\n                responseTime\n            });\n            if (result.success && result.result) {\n                // Update session with new question\n                if (result.result.nextQuestion) {\n                    setSession((prev)=>prev ? {\n                            ...prev,\n                            currentQuestion: result.result.nextQuestion,\n                            messages: [\n                                ...prev.messages,\n                                {\n                                    role: \"user\",\n                                    content: currentResponse,\n                                    timestamp: Date.now()\n                                },\n                                {\n                                    role: \"assistant\",\n                                    content: result.result.nextQuestion.message,\n                                    timestamp: Date.now()\n                                }\n                            ]\n                        } : null);\n                }\n                // Update progress\n                setProgress(result.result.progress);\n                // Clear response\n                setCurrentResponse(\"\");\n                // Check if session is complete\n                if (result.result.sessionComplete) {\n                    handleSessionComplete();\n                }\n            } else {\n                console.error(\"Failed to process response:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"Error submitting response:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSessionComplete = ()=>{\n        // Navigate to results page\n        router.push(\"/dashboard/coaching/results?sessionId=\".concat(session === null || session === void 0 ? void 0 : session.id));\n    };\n    const pauseSession = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.pauseSession(session.id);\n            if (result.success) {\n                setSession((prev)=>prev ? {\n                        ...prev,\n                        status: \"paused\"\n                    } : null);\n            }\n        }\n    };\n    const resumeSession = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.resumeSession(session.id);\n            if (result.success) {\n                setSession((prev)=>prev ? {\n                        ...prev,\n                        status: \"active\"\n                    } : null);\n            }\n        }\n    };\n    const endSession = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.cancelSession(session.id);\n            router.push(\"/dashboard/experts\");\n        }\n    };\n    // Mock functions for media capture\n    const captureAudioSnippet = async ()=>{\n        return new Blob([\n            \"mock audio\"\n        ], {\n            type: \"audio/wav\"\n        });\n    };\n    const captureVideoSnippet = async ()=>{\n        return new Blob([\n            \"mock video\"\n        ], {\n            type: \"video/mp4\"\n        });\n    };\n    const getEmotionalColor = function(value) {\n        let isStress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (isStress) {\n            if (value > 70) return \"text-red-600\";\n            if (value > 40) return \"text-yellow-600\";\n            return \"text-green-600\";\n        } else {\n            if (value > 80) return \"text-green-600\";\n            if (value > 60) return \"text-yellow-600\";\n            return \"text-red-600\";\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, \"0\"));\n    };\n    if (isLoading && !session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-blue-600 animate-pulse mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Initializing AI Coach...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                        lineNumber: 330,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Setting up your personalized coaching session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                lineNumber: 328,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n            lineNumber: 327,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>router.push(\"/dashboard/experts\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Experts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: [\n                                                \"AI Coach - \",\n                                                role.replace(\"-\", \" \").toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        (session === null || session === void 0 ? void 0 : session.status) === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Live\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                (session === null || session === void 0 ? void 0 : session.status) === \"active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: pauseSession,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Pause\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: resumeSession,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Resume\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: endSession,\n                                    variant: \"destructive\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 379,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"End Session\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 378,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 space-y-6\",\n                            children: [\n                                progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Session Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 393,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            progress.currentQuestionIndex,\n                                                            \" / \",\n                                                            progress.totalQuestions,\n                                                            \" questions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 392,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                value: progress.completionPercentage,\n                                                className: \"mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Time: \",\n                                                            formatTime(progress.timeElapsed)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Avg Score: \",\n                                                            progress.averageScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Difficulty: \",\n                                                            progress.currentDifficulty,\n                                                            \"/10\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 15\n                                }, this),\n                                (session === null || session === void 0 ? void 0 : session.currentQuestion) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Current Question\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: session.currentQuestion.questionType\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: [\n                                                                    \"Difficulty: \",\n                                                                    session.currentQuestion.difficulty,\n                                                                    \"/10\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 412,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg leading-relaxed\",\n                                                        children: session.currentQuestion.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    session.currentQuestion.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    \"Category: \",\n                                                                    session.currentQuestion.category\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    showHints && session.currentQuestion.hints && session.currentQuestion.hints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-yellow-800\",\n                                                                        children: \"Hints\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 446,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 444,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-yellow-700 space-y-1\",\n                                                                children: session.currentQuestion.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"• \",\n                                                                            hint\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 448,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Your Response\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 465,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 463,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 462,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                        ref: responseTextareaRef,\n                                                        value: currentResponse,\n                                                        onChange: (e)=>setCurrentResponse(e.target.value),\n                                                        placeholder: \"Type your response here... Be specific and explain your thinking process.\",\n                                                        rows: 6,\n                                                        className: \"resize-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 470,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>setShowHints(!showHints),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                                lineNumber: 486,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            showHints ? \"Hide Hints\" : \"Show Hints\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 481,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                                lineNumber: 491,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Voice Input\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 490,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 480,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: submitResponse,\n                                                                disabled: !currentResponse.trim() || isLoading,\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                            lineNumber: 503,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Processing...\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Submit Response\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 496,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 469,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 525,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Live Metrics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 523,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Confidence\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 532,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.confidence)),\n                                                                    children: [\n                                                                        emotionalState.confidence,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 533,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 531,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.confidence,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 537,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Stress Level\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 542,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.stress, true)),\n                                                                    children: [\n                                                                        emotionalState.stress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 543,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 541,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.stress,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 547,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Engagement\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 552,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.engagement)),\n                                                                    children: [\n                                                                        emotionalState.engagement,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 553,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.engagement,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Clarity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 562,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.clarity)),\n                                                                    children: [\n                                                                        emotionalState.clarity,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 563,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 561,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.clarity,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 567,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 529,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 522,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Session Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 575,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Questions Answered\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 584,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: progress.currentQuestionIndex\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 585,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 583,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Average Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 588,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    progress.averageScore,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 589,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Time Elapsed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 592,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: formatTime(progress.timeElapsed)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 593,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Current Difficulty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 596,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    progress.currentDifficulty,\n                                                                    \"/10\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 597,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 595,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 573,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 text-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 608,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Media\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 609,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 614,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 36\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 616,\n                                                                columnNumber: 68\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 615,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 613,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Microphone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 620,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 36\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 622,\n                                                                columnNumber: 66\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 621,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 619,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: isRecording ? \"Recording for emotional analysis\" : \"Media access disabled\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 625,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 612,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 605,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 520,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                    lineNumber: 385,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n            lineNumber: 339,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n        lineNumber: 338,\n        columnNumber: 5\n    }, this);\n}\n_s(AICoachingPage, \"/6ARdghxOLQk9Ygn5SpuWlqluZo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AICoachingPage;\nvar _c;\n$RefreshReg$(_c, \"AICoachingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/coaching/ai/page.tsx\n"));

/***/ })

});