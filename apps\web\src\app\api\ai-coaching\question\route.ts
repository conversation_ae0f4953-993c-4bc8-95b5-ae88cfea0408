import { NextRequest, NextResponse } from 'next/server'
import { aiCoachingSessionService } from '@/services/aiCoachingSessionService'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, userResponse } = body

    // Validate required fields
    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing required field: sessionId' },
        { status: 400 }
      )
    }

    // Generate next question
    const nextQuestion = await aiCoachingSessionService.generateNextQuestion(
      sessionId,
      userResponse
    )

    return NextResponse.json({
      success: true,
      question: nextQuestion
    })
  } catch (error) {
    console.error('Error generating next question:', error)
    
    if (error instanceof Error && error.message.includes('Session')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to generate next question' },
      { status: 500 }
    )
  }
}
