"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/resume/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js":
/*!********************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/alert-triangle.js ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AlertTriangle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.312.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst AlertTriangle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"AlertTriangle\", [\n    [\n        \"path\",\n        {\n            d: \"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z\",\n            key: \"c3ski4\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 9v4\",\n            key: \"juzpu7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 17h.01\",\n            key: \"p32p05\"\n        }\n    ]\n]);\n //# sourceMappingURL=alert-triangle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/brain.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Brain; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.312.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Brain = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Brain\", [\n    [\n        \"path\",\n        {\n            d: \"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z\",\n            key: \"1mhkh5\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z\",\n            key: \"1d6s00\"\n        }\n    ]\n]);\n //# sourceMappingURL=brain.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js":
/*!***************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/lightbulb.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Lightbulb; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.312.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Lightbulb = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Lightbulb\", [\n    [\n        \"path\",\n        {\n            d: \"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5\",\n            key: \"1gvzjb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M9 18h6\",\n            key: \"x1upvd\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 22h4\",\n            key: \"ceow96\"\n        }\n    ]\n]);\n //# sourceMappingURL=lightbulb.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvbGlnaHRidWxiLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sTUFBQUEsWUFBWUMsZ0VBQWdCQSxDQUFDLGFBQWE7SUFDOUM7UUFDRTtRQUNBO1lBQ0VDLEdBQUc7WUFDSEMsS0FBSztRQUNQO0tBQ0Y7SUFDQTtRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUFXQyxLQUFLO1FBQUE7S0FBVTtJQUN4QztRQUFDO1FBQVE7WUFBRUQsR0FBRztZQUFZQyxLQUFLO1FBQUE7S0FBVTtDQUMxQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi4vLi4vLi4vc3JjL2ljb25zL2xpZ2h0YnVsYi50cz83ZTlmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBjcmVhdGVMdWNpZGVJY29uIGZyb20gJy4uL2NyZWF0ZUx1Y2lkZUljb24nO1xuXG4vKipcbiAqIEBjb21wb25lbnQgQG5hbWUgTGlnaHRidWxiXG4gKiBAZGVzY3JpcHRpb24gTHVjaWRlIFNWRyBpY29uIGNvbXBvbmVudCwgcmVuZGVycyBTVkcgRWxlbWVudCB3aXRoIGNoaWxkcmVuLlxuICpcbiAqIEBwcmV2aWV3ICFbaW1nXShkYXRhOmltYWdlL3N2Zyt4bWw7YmFzZTY0LFBITjJaeUFnZUcxc2JuTTlJbWgwZEhBNkx5OTNkM2N1ZHpNdWIzSm5Mekl3TURBdmMzWm5JZ29nSUhkcFpIUm9QU0l5TkNJS0lDQm9aV2xuYUhROUlqSTBJZ29nSUhacFpYZENiM2c5SWpBZ01DQXlOQ0F5TkNJS0lDQm1hV3hzUFNKdWIyNWxJZ29nSUhOMGNtOXJaVDBpSXpBd01DSWdjM1I1YkdVOUltSmhZMnRuY205MWJtUXRZMjlzYjNJNklDTm1abVk3SUdKdmNtUmxjaTF5WVdScGRYTTZJREp3ZUNJS0lDQnpkSEp2YTJVdGQybGtkR2c5SWpJaUNpQWdjM1J5YjJ0bExXeHBibVZqWVhBOUluSnZkVzVrSWdvZ0lITjBjbTlyWlMxc2FXNWxhbTlwYmowaWNtOTFibVFpQ2o0S0lDQThjR0YwYUNCa1BTSk5NVFVnTVRSakxqSXRNU0F1TnkweExqY2dNUzQxTFRJdU5TQXhMUzQ1SURFdU5TMHlMaklnTVM0MUxUTXVOVUUySURZZ01DQXdJREFnTmlBNFl6QWdNU0F1TWlBeUxqSWdNUzQxSURNdU5TNDNMamNnTVM0eklERXVOU0F4TGpVZ01pNDFJaUF2UGdvZ0lEeHdZWFJvSUdROUlrMDVJREU0YURZaUlDOCtDaUFnUEhCaGRHZ2daRDBpVFRFd0lESXlhRFFpSUM4K0Nqd3ZjM1puUGdvPSkgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvbGlnaHRidWxiXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgTGlnaHRidWxiID0gY3JlYXRlTHVjaWRlSWNvbignTGlnaHRidWxiJywgW1xuICBbXG4gICAgJ3BhdGgnLFxuICAgIHtcbiAgICAgIGQ6ICdNMTUgMTRjLjItMSAuNy0xLjcgMS41LTIuNSAxLS45IDEuNS0yLjIgMS41LTMuNUE2IDYgMCAwIDAgNiA4YzAgMSAuMiAyLjIgMS41IDMuNS43LjcgMS4zIDEuNSAxLjUgMi41JyxcbiAgICAgIGtleTogJzFndnpqYicsXG4gICAgfSxcbiAgXSxcbiAgWydwYXRoJywgeyBkOiAnTTkgMThoNicsIGtleTogJ3gxdXB2ZCcgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00xMCAyMmg0Jywga2V5OiAnY2Vvdzk2JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBMaWdodGJ1bGI7XG4iXSwibmFtZXMiOlsiTGlnaHRidWxiIiwiY3JlYXRlTHVjaWRlSWNvbiIsImQiLCJrZXkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js":
/*!**********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/scan.js ***!
  \**********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Scan; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.312.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Scan = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Scan\", [\n    [\n        \"path\",\n        {\n            d: \"M3 7V5a2 2 0 0 1 2-2h2\",\n            key: \"aa7l1z\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M17 3h2a2 2 0 0 1 2 2v2\",\n            key: \"4qcy5o\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M21 17v2a2 2 0 0 1-2 2h-2\",\n            key: \"6vwrx8\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M7 21H5a2 2 0 0 1-2-2v-2\",\n            key: \"ioqczr\"\n        }\n    ]\n]);\n //# sourceMappingURL=scan.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js":
/*!************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/search.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Search; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.312.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Search = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Search\", [\n    [\n        \"circle\",\n        {\n            cx: \"11\",\n            cy: \"11\",\n            r: \"8\",\n            key: \"4ej97u\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m21 21-4.3-4.3\",\n            key: \"1qie3q\"\n        }\n    ]\n]);\n //# sourceMappingURL=search.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/x-circle.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ XCircle; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.312.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst XCircle = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"XCircle\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m15 9-6 6\",\n            key: \"1uzhvr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m9 9 6 6\",\n            key: \"z0biqf\"\n        }\n    ]\n]);\n //# sourceMappingURL=x-circle.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/dashboard/resume/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/resume/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ResumePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_resume_ATSOptimizationTab__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/resume/ATSOptimizationTab */ \"(app-pages-browser)/./src/components/resume/ATSOptimizationTab.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction ResumePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [resumes, setResumes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedResume, setSelectedResume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load user's resumes\n        loadResumes();\n    }, []);\n    const loadResumes = async ()=>{\n        try {\n            setIsLoading(true);\n            // TODO: Implement API call to load resumes\n            // const response = await apiClient.getResumes()\n            // setResumes(response)\n            // Mock data for now - will be replaced with real API call\n            setResumes([\n                {\n                    id: \"1\",\n                    name: \"Software Engineer Resume\",\n                    fileName: \"john_doe_resume.pdf\",\n                    uploadedAt: new Date(\"2024-01-15\"),\n                    atsScore: 85,\n                    status: \"analyzed\",\n                    fileSize: \"245 KB\",\n                    keywords: [\n                        \"JavaScript\",\n                        \"React\",\n                        \"Node.js\",\n                        \"Python\"\n                    ],\n                    suggestions: 3\n                },\n                {\n                    id: \"2\",\n                    name: \"Senior Developer Resume\",\n                    fileName: \"john_doe_senior.pdf\",\n                    uploadedAt: new Date(\"2024-01-10\"),\n                    atsScore: 72,\n                    status: \"needs_improvement\",\n                    fileSize: \"198 KB\",\n                    keywords: [\n                        \"Java\",\n                        \"Spring\",\n                        \"AWS\",\n                        \"Docker\"\n                    ],\n                    suggestions: 7\n                }\n            ]);\n        } catch (error) {\n            console.error(\"Failed to load resumes:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const getScoreBadgeVariant = (score)=>{\n        if (score >= 80) return \"default\";\n        if (score >= 60) return \"secondary\";\n        return \"destructive\";\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"analyzed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                    lineNumber: 94,\n                    columnNumber: 16\n                }, this);\n            case \"needs_improvement\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 16\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-600\",\n                                children: \"Resume Manager\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"Upload, analyze, and optimize your resumes for ATS systems\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>router.push(\"/dashboard/resume/builder\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Create Resume\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>router.push(\"/dashboard/resume/upload\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Upload Resume\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                defaultValue: \"overview\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"overview\",\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"resumes\",\n                                children: \"My Resumes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"analytics\",\n                                children: \"Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"optimization\",\n                                children: \"ATS Optimization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Total Resumes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 140,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 138,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: resumes.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"+1 from last month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 142,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Average ATS Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: \"78.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"+5.2% from last month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 156,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 154,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Optimizations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: \"10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Suggestions applied\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 168,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Success Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: \"92%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Interview callback rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Your latest resume uploads and optimizations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 189,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: resumes.slice(0, 3).map((resume)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: getStatusIcon(resume.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                            lineNumber: 199,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600 truncate\",\n                                                                    children: resume.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 203,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"Uploaded \",\n                                                                        resume.uploadedAt.toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 206,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: getScoreBadgeVariant(resume.atsScore),\n                                                                children: [\n                                                                    \"ATS: \",\n                                                                    resume.atsScore,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 211,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                            lineNumber: 210,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, resume.id, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"resumes\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                resumes.map((resume)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"hover:shadow-lg transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"text-lg\",\n                                                                children: resume.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 229,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            getStatusIcon(resume.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                        children: resume.fileName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"ATS Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold \".concat(getScoreColor(resume.atsScore)),\n                                                                children: [\n                                                                    resume.atsScore,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 237,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                                        value: resume.atsScore,\n                                                        className: \"h-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: [\n                                                            resume.keywords.slice(0, 3).map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: keyword\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 245,\n                                                                    columnNumber: 23\n                                                                }, this)),\n                                                            resume.keywords.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    resume.keywords.length - 3,\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: resume.fileSize\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    resume.suggestions,\n                                                                    \" suggestions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"View\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 262,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                        lineNumber: 267,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Edit\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 266,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 271,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 270,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 274,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 273,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, resume.id, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"border-dashed border-2 hover:border-blue-500 transition-colors cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"flex flex-col items-center justify-center h-full p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-600 mb-2\",\n                                                children: \"Upload New Resume\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 text-center mb-4\",\n                                                children: \"Drag and drop your resume or click to browse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>router.push(\"/dashboard/resume/upload\"),\n                                                children: \"Choose File\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                            lineNumber: 224,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"analytics\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Resume Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Detailed insights into your resume performance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-600 mb-2\",\n                                                children: \"Analytics Dashboard Coming Soon\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500\",\n                                                children: \"We're building comprehensive analytics to help you track your resume performance.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 311,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                    lineNumber: 305,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"optimization\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_resume_ATSOptimizationTab__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                            lineNumber: 320,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_s(ResumePage, \"mku4Hi+EWsDiK68U1j353Ei/xmY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ResumePage;\nvar _c;\n$RefreshReg$(_c, \"ResumePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/resume/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/resume/ATSOptimizationTab.tsx":
/*!******************************************************!*\
  !*** ./src/components/resume/ATSOptimizationTab.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ATSOptimizationTab; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scan.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,CheckCircle,Eye,Lightbulb,Plus,Scan,Search,Target,TrendingUp,Upload,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Mock data for demonstration\nconst mockAnalysis = {\n    id: \"1\",\n    fileName: \"john_doe_resume.pdf\",\n    uploadDate: \"2024-01-20T10:30:00Z\",\n    atsScore: {\n        overall: 78,\n        formatting: 85,\n        keywords: 72,\n        structure: 80,\n        readability: 76\n    },\n    suggestions: [\n        {\n            id: \"1\",\n            type: \"critical\",\n            category: \"keywords\",\n            title: \"Missing Key Technical Skills\",\n            description: \"Your resume lacks important keywords that appear in the job description\",\n            impact: \"high\",\n            effort: \"easy\",\n            before: \"Developed web applications\",\n            after: \"Developed React.js web applications using TypeScript and Node.js\"\n        },\n        {\n            id: \"2\",\n            type: \"important\",\n            category: \"formatting\",\n            title: \"Use Standard Section Headers\",\n            description: 'ATS systems prefer standard section headers like \"Work Experience\" over creative alternatives',\n            impact: \"medium\",\n            effort: \"easy\",\n            before: \"Professional Journey\",\n            after: \"Work Experience\"\n        },\n        {\n            id: \"3\",\n            type: \"minor\",\n            category: \"structure\",\n            title: \"Add Skills Section\",\n            description: \"Include a dedicated skills section for better keyword recognition\",\n            impact: \"medium\",\n            effort: \"moderate\"\n        }\n    ],\n    keywords: {\n        found: [\n            \"JavaScript\",\n            \"React\",\n            \"Project Management\",\n            \"Team Leadership\"\n        ],\n        missing: [\n            \"TypeScript\",\n            \"Node.js\",\n            \"AWS\",\n            \"Agile\",\n            \"Scrum\"\n        ],\n        density: {\n            \"JavaScript\": 3.2,\n            \"React\": 2.8,\n            \"Project Management\": 1.5,\n            \"Team Leadership\": 1.2\n        }\n    },\n    jobMatch: 72,\n    status: \"completed\"\n};\nfunction ATSOptimizationTab() {\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"scanner\");\n    const [isAnalyzing, setIsAnalyzing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentAnalysis, setCurrentAnalysis] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedJobDescription, setSelectedJobDescription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Simulate loading analysis\n        setCurrentAnalysis(mockAnalysis);\n    }, []);\n    const handleFileUpload = async (file)=>{\n        setIsAnalyzing(true);\n        // Simulate analysis process\n        setTimeout(()=>{\n            setCurrentAnalysis(mockAnalysis);\n            setIsAnalyzing(false);\n        }, 3000);\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 80) return \"text-green-600 dark:text-green-400\";\n        if (score >= 60) return \"text-yellow-600 dark:text-yellow-400\";\n        return \"text-red-600 dark:text-red-400\";\n    };\n    const getScoreBgColor = (score)=>{\n        if (score >= 80) return \"bg-green-100 dark:bg-green-950/20\";\n        if (score >= 60) return \"bg-yellow-100 dark:bg-yellow-950/20\";\n        return \"bg-red-100 dark:bg-red-950/20\";\n    };\n    const getSuggestionIcon = (type)=>{\n        switch(type){\n            case \"critical\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 31\n                }, this);\n            case \"important\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 32\n                }, this);\n            case \"minor\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 28\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                    lineNumber: 159,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getSuggestionBadgeColor = (type)=>{\n        switch(type){\n            case \"critical\":\n                return \"bg-red-100 dark:bg-red-950/20 text-red-800 dark:text-red-400 border-red-200 dark:border-red-800\";\n            case \"important\":\n                return \"bg-yellow-100 dark:bg-yellow-950/20 text-yellow-800 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800\";\n            case \"minor\":\n                return \"bg-blue-100 dark:bg-blue-950/20 text-blue-800 dark:text-blue-400 border-blue-200 dark:border-blue-800\";\n            default:\n                return \"bg-green-100 dark:bg-green-950/20 text-green-800 dark:text-green-400 border-green-200 dark:border-green-800\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-4 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-blue-600 dark:text-blue-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    (currentAnalysis === null || currentAnalysis === void 0 ? void 0 : currentAnalysis.atsScore.overall) || 0,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 181,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"ATS Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                lineNumber: 178,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-600 dark:text-green-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: (currentAnalysis === null || currentAnalysis === void 0 ? void 0 : currentAnalysis.keywords.found.length) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Keywords Found\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-8 w-8 text-purple-600 dark:text-purple-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: [\n                                                    (currentAnalysis === null || currentAnalysis === void 0 ? void 0 : currentAnalysis.jobMatch) || 0,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Job Match\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-8 w-8 text-yellow-600 dark:text-yellow-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl font-bold text-foreground\",\n                                                children: (currentAnalysis === null || currentAnalysis === void 0 ? void 0 : currentAnalysis.suggestions.length) || 0\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                value: activeTab,\n                onValueChange: setActiveTab,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"scanner\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Scanner\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"analysis\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Analysis\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"keywords\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Keywords\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"suggestions\",\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"Suggestions\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"scanner\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Resume Scanner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                    lineNumber: 257,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: isAnalyzing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-foreground mb-2\",\n                                                children: \"Analyzing Your Resume\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-muted-foreground\",\n                                                children: \"Our AI is scanning your resume for ATS compatibility...\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 max-w-md mx-auto\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                                                        value: 65,\n                                                        className: \"h-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-muted-foreground mt-2\",\n                                                        children: \"Analyzing keywords and formatting...\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 265,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border-2 border-dashed border-border rounded-lg p-8 text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-12 w-12 text-muted-foreground mx-auto mb-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-lg font-medium text-foreground mb-2\",\n                                                        children: \"Upload Your Resume\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-4\",\n                                                        children: \"Drag and drop your resume or click to browse\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: ()=>{\n                                                                    const input = document.createElement(\"input\");\n                                                                    input.type = \"file\";\n                                                                    input.accept = \".pdf,.doc,.docx\";\n                                                                    input.onchange = (e)=>{\n                                                                        var _e_target_files;\n                                                                        const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n                                                                        if (file) handleFileUpload(file);\n                                                                    };\n                                                                    input.click();\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 298,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    \"Choose File\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 286,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Supports PDF, DOC, DOCX\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 301,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                                className: \"text-lg\",\n                                                                children: \"Job Description (Optional)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground\",\n                                                                children: \"Paste the job description to get targeted optimization suggestions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 311,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                                value: selectedJobDescription,\n                                                                onChange: (e)=>setSelectedJobDescription(e.target.value),\n                                                                placeholder: \"Paste the job description here for more accurate analysis...\",\n                                                                className: \"w-full h-32 p-3 border border-input bg-background text-foreground rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-ring\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 316,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mt-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            selectedJobDescription.length,\n                                                                            \" characters\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                                lineNumber: 327,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            \"Analyze with AI\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 326,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 322,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"analysis\",\n                        className: \"space-y-6\",\n                        children: currentAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 345,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"ATS Compatibility Score\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 346,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 344,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl font-bold \".concat(getScoreColor(currentAnalysis.atsScore.overall), \" mb-2\"),\n                                                        children: [\n                                                            currentAnalysis.atsScore.overall,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: \"Overall Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                                                        value: currentAnalysis.atsScore.overall,\n                                                        className: \"mt-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 19\n                                            }, this),\n                                            Object.entries(currentAnalysis.atsScore).filter((param)=>{\n                                                let [key] = param;\n                                                return key !== \"overall\";\n                                            }).map((param)=>{\n                                                let [key, value] = param;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold \".concat(getScoreColor(value), \" mb-2\"),\n                                                            children: [\n                                                                value,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-muted-foreground capitalize\",\n                                                            children: key.replace(/([A-Z])/g, \" $1\").trim()\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_5__.Progress, {\n                                                            value: value,\n                                                            className: \"mt-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, key, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this);\n                                            })\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 350,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"keywords\",\n                        className: \"space-y-6\",\n                        children: currentAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 385,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Found Keywords\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: currentAnalysis.keywords.found.map((keyword, index)=>{\n                                                    var _currentAnalysis_keywords_density_keyword;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-2 bg-green-50 dark:bg-green-950/20 rounded border border-green-200 dark:border-green-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-foreground\",\n                                                                children: keyword\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 393,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                className: \"bg-green-100 dark:bg-green-950/20 text-green-800 dark:text-green-400\",\n                                                                children: [\n                                                                    ((_currentAnalysis_keywords_density_keyword = currentAnalysis.keywords.density[keyword]) === null || _currentAnalysis_keywords_density_keyword === void 0 ? void 0 : _currentAnalysis_keywords_density_keyword.toFixed(1)) || \"0.0\",\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 394,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 23\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Missing Keywords\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 405,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                            lineNumber: 404,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: currentAnalysis.keywords.missing.map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between p-2 bg-red-50 dark:bg-red-950/20 rounded border border-red-200 dark:border-red-800\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-foreground\",\n                                                                children: keyword\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                className: \"text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"h-3 w-3 mr-1\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 416,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Add\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 411,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                            lineNumber: 381,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"suggestions\",\n                        className: \"space-y-6\",\n                        children: currentAnalysis && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5 text-primary\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 434,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Optimization Suggestions\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 435,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: currentAnalysis.suggestions.map((suggestion)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-border rounded-lg p-4 bg-card\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start justify-between mb-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start space-x-3\",\n                                                                children: [\n                                                                    getSuggestionIcon(suggestion.type),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                className: \"font-medium text-foreground\",\n                                                                                children: suggestion.title\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                                lineNumber: 446,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-sm text-muted-foreground mt-1\",\n                                                                                children: suggestion.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                                lineNumber: 447,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 445,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        className: getSuggestionBadgeColor(suggestion.type),\n                                                                        children: suggestion.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: [\n                                                                            suggestion.impact,\n                                                                            \" impact\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 454,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    suggestion.before && suggestion.after && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-red-50 dark:bg-red-950/20 rounded border border-red-200 dark:border-red-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-sm font-medium text-red-800 dark:text-red-400 mb-2\",\n                                                                        children: \"Before\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-foreground\",\n                                                                        children: suggestion.before\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 464,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 bg-green-50 dark:bg-green-950/20 rounded border border-green-200 dark:border-green-800\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                        className: \"text-sm font-medium text-green-800 dark:text-green-400 mb-2\",\n                                                                        children: \"After\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 467,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-foreground\",\n                                                                        children: suggestion.after\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                        lineNumber: 468,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-end mt-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center space-x-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    variant: \"outline\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                            lineNumber: 476,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Preview\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                    size: \"sm\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_CheckCircle_Eye_Lightbulb_Plus_Scan_Search_Target_TrendingUp_Upload_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                            className: \"h-3 w-3 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                            lineNumber: 480,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        \"Apply Fix\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                            lineNumber: 474,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, suggestion.id, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                                lineNumber: 441,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n                lineNumber: 234,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\resume\\\\ATSOptimizationTab.tsx\",\n        lineNumber: 173,\n        columnNumber: 5\n    }, this);\n}\n_s(ATSOptimizationTab, \"Y+j7iZ7E9x+Uk//PNfQr+nLnUMs=\");\n_c = ATSOptimizationTab;\nvar _c;\n$RefreshReg$(_c, \"ATSOptimizationTab\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/resume/ATSOptimizationTab.tsx\n"));

/***/ })

});