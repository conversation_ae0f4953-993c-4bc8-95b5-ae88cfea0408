import { NextRequest, NextResponse } from 'next/server'

interface User {
  id: string
  firstName: string
  lastName: string
  email: string
  role: string
  department: string
  status: 'active' | 'inactive' | 'pending'
  avatar?: string
  phoneNumber?: string
  lastLogin?: string
  createdAt: string
  updatedAt: string
  permissions: string[]
  passwordHash?: string
}

interface CreateUserRequest {
  firstName: string
  lastName: string
  email: string
  password: string
  role: string
  department: string
  phoneNumber?: string
  bio?: string
  sendWelcomeEmail: boolean
  requirePasswordChange: boolean
  isActive: boolean
  permissions: string[]
}

// Mock user data - in production, this would be stored in a database
let users: User[] = [
  {
    id: '1',
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    role: 'admin',
    department: 'Engineering',
    status: 'active',
    avatar: '',
    phoneNumber: '+****************',
    lastLogin: '2024-01-20T10:30:00Z',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T10:30:00Z',
    permissions: ['all']
  },
  {
    id: '2',
    firstName: 'Jane',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    role: 'expert',
    department: 'Coaching',
    status: 'active',
    avatar: '',
    phoneNumber: '+****************',
    lastLogin: '2024-01-19T15:45:00Z',
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-19T15:45:00Z',
    permissions: ['coaching', 'sessions', 'analytics']
  },
  {
    id: '3',
    firstName: 'Mike',
    lastName: 'Johnson',
    email: '<EMAIL>',
    role: 'manager',
    department: 'Operations',
    status: 'active',
    avatar: '',
    phoneNumber: '+****************',
    lastLogin: '2024-01-18T09:15:00Z',
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-18T09:15:00Z',
    permissions: ['user_management', 'analytics', 'reports']
  }
]

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const role = searchParams.get('role') || 'all'
    const status = searchParams.get('status') || 'all'

    // Check if user has admin permissions
    const isAdmin = true // Replace with actual admin check

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Filter users based on search criteria
    let filteredUsers = users.filter(user => {
      const matchesSearch = search === '' || 
        user.firstName.toLowerCase().includes(search.toLowerCase()) ||
        user.lastName.toLowerCase().includes(search.toLowerCase()) ||
        user.email.toLowerCase().includes(search.toLowerCase()) ||
        user.department.toLowerCase().includes(search.toLowerCase())
      
      const matchesRole = role === 'all' || user.role === role
      const matchesStatus = status === 'all' || user.status === status

      return matchesSearch && matchesRole && matchesStatus
    })

    // Pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex)

    // Remove sensitive data
    const safeUsers = paginatedUsers.map(user => {
      const { passwordHash, ...safeUser } = user
      return safeUser
    })

    return NextResponse.json({
      success: true,
      users: safeUsers,
      pagination: {
        page,
        limit,
        total: filteredUsers.length,
        totalPages: Math.ceil(filteredUsers.length / limit)
      },
      stats: {
        total: users.length,
        active: users.filter(u => u.status === 'active').length,
        pending: users.filter(u => u.status === 'pending').length,
        inactive: users.filter(u => u.status === 'inactive').length,
        admins: users.filter(u => u.role === 'admin').length
      }
    })
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const isAdmin = true // Replace with actual admin check

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body: CreateUserRequest = await request.json()

    // Validate required fields
    const { firstName, lastName, email, password, role } = body
    if (!firstName || !lastName || !email || !password || !role) {
      return NextResponse.json(
        { error: 'Missing required fields: firstName, lastName, email, password, role' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Check if email already exists
    const existingUser = users.find(u => u.email.toLowerCase() === email.toLowerCase())
    if (existingUser) {
      return NextResponse.json(
        { error: 'Email address already exists' },
        { status: 409 }
      )
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    // Validate role
    const validRoles = ['admin', 'expert', 'manager', 'user']
    if (!validRoles.includes(role)) {
      return NextResponse.json(
        { error: 'Invalid role specified' },
        { status: 400 }
      )
    }

    // Create new user
    const newUser: User = {
      id: (users.length + 1).toString(),
      firstName,
      lastName,
      email,
      role,
      department: body.department || 'General',
      status: body.isActive ? 'active' : 'pending',
      avatar: '',
      phoneNumber: body.phoneNumber || '',
      lastLogin: null,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      permissions: body.permissions || [],
      passwordHash: 'hashed_' + password // In production, use proper password hashing
    }

    users.push(newUser)

    // Send welcome email if requested
    if (body.sendWelcomeEmail) {
      console.log(`Sending welcome email to ${email}`)
      // Implement email sending logic
    }

    // Log the user creation for audit purposes
    console.log(`User created: ${email} by admin`, {
      userId: newUser.id,
      role: newUser.role,
      timestamp: new Date().toISOString()
    })

    // Return user without sensitive data
    const { passwordHash, ...safeUser } = newUser
    return NextResponse.json({
      success: true,
      user: safeUser,
      message: 'User created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const isAdmin = true // Replace with actual admin check

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const { userId, ...updates } = body

    if (!userId) {
      return NextResponse.json(
        { error: 'User ID is required' },
        { status: 400 }
      )
    }

    // Find user
    const userIndex = users.findIndex(u => u.id === userId)
    if (userIndex === -1) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Validate email if being updated
    if (updates.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      if (!emailRegex.test(updates.email)) {
        return NextResponse.json(
          { error: 'Invalid email format' },
          { status: 400 }
        )
      }

      // Check if email already exists (excluding current user)
      const existingUser = users.find(u => 
        u.email.toLowerCase() === updates.email.toLowerCase() && u.id !== userId
      )
      if (existingUser) {
        return NextResponse.json(
          { error: 'Email address already exists' },
          { status: 409 }
        )
      }
    }

    // Update user
    users[userIndex] = {
      ...users[userIndex],
      ...updates,
      updatedAt: new Date().toISOString()
    }

    // Log the update for audit purposes
    console.log(`User updated: ${users[userIndex].email} by admin`, {
      userId,
      fields: Object.keys(updates),
      timestamp: new Date().toISOString()
    })

    // Return updated user without sensitive data
    const { passwordHash, ...safeUser } = users[userIndex]
    return NextResponse.json({
      success: true,
      user: safeUser,
      message: 'User updated successfully'
    })

  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { error: 'Failed to update user' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const isAdmin = true // Replace with actual admin check

    if (!isAdmin) {
      return NextResponse.json(
        { error: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const userIds = searchParams.get('userIds')?.split(',')

    if (!userId && !userIds) {
      return NextResponse.json(
        { error: 'User ID(s) required' },
        { status: 400 }
      )
    }

    const idsToDelete = userId ? [userId] : userIds || []

    // Prevent deletion of the last admin
    const remainingUsers = users.filter(u => !idsToDelete.includes(u.id))
    const remainingAdmins = remainingUsers.filter(u => u.role === 'admin')
    
    if (remainingAdmins.length === 0) {
      return NextResponse.json(
        { error: 'Cannot delete the last administrator' },
        { status: 400 }
      )
    }

    // Delete users
    const deletedUsers = users.filter(u => idsToDelete.includes(u.id))
    users = users.filter(u => !idsToDelete.includes(u.id))

    // Log the deletion for audit purposes
    deletedUsers.forEach(user => {
      console.log(`User deleted: ${user.email} by admin`, {
        userId: user.id,
        timestamp: new Date().toISOString()
      })
    })

    return NextResponse.json({
      success: true,
      deletedCount: deletedUsers.length,
      message: `${deletedUsers.length} user(s) deleted successfully`
    })

  } catch (error) {
    console.error('Error deleting user(s):', error)
    return NextResponse.json(
      { error: 'Failed to delete user(s)' },
      { status: 500 }
    )
  }
}
