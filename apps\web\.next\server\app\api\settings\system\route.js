"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/settings/system/route";
exports.ids = ["app/api/settings/system/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fsystem%2Froute&page=%2Fapi%2Fsettings%2Fsystem%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fsystem%2Froute.ts&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fsystem%2Froute&page=%2Fapi%2Fsettings%2Fsystem%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fsystem%2Froute.ts&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_apps_InterviewSpark_apps_web_src_app_api_settings_system_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/settings/system/route.ts */ \"(rsc)/./src/app/api/settings/system/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/settings/system/route\",\n        pathname: \"/api/settings/system\",\n        filename: \"route\",\n        bundlePath: \"app/api/settings/system/route\"\n    },\n    resolvedPagePath: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\settings\\\\system\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_apps_InterviewSpark_apps_web_src_app_api_settings_system_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/settings/system/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fsystem%2Froute&page=%2Fapi%2Fsettings%2Fsystem%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fsystem%2Froute.ts&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/settings/system/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/settings/system/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Mock system settings - in production, this would be stored in a database\nlet systemSettings = {\n    siteName: \"InterviewSpark\",\n    siteDescription: \"AI-Powered Interview Preparation Platform\",\n    maintenanceMode: false,\n    registrationEnabled: true,\n    emailVerificationRequired: true,\n    maxFileSize: 10,\n    sessionTimeout: 30,\n    backupFrequency: \"daily\",\n    logLevel: \"info\"\n};\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const type = searchParams.get(\"type\");\n        // Check if user has admin permissions\n        const isAdmin = true // Replace with actual admin check\n        ;\n        if (!isAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        if (type === \"metrics\") {\n            // Return system metrics\n            const metrics = {\n                totalUsers: 1264,\n                activeUsers: 342,\n                totalSessions: 5847,\n                apiCalls: 125847,\n                storageUsed: \"2.4 GB\",\n                uptime: \"99.9%\",\n                serverStatus: {\n                    webServer: \"online\",\n                    database: \"online\",\n                    redis: \"online\",\n                    storage: \"online\"\n                },\n                resourceUsage: {\n                    cpu: 23,\n                    memory: 67,\n                    disk: 45\n                }\n            };\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                metrics\n            });\n        }\n        // Return system settings\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            settings: systemSettings\n        });\n    } catch (error) {\n        console.error(\"Error fetching system settings:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch system settings\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        const isAdmin = true // Replace with actual admin check\n        ;\n        if (!isAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        // Validate settings\n        if (body.siteName && typeof body.siteName !== \"string\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid site name\"\n            }, {\n                status: 400\n            });\n        }\n        if (body.maxFileSize && (typeof body.maxFileSize !== \"number\" || body.maxFileSize < 1 || body.maxFileSize > 100)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Max file size must be between 1 and 100 MB\"\n            }, {\n                status: 400\n            });\n        }\n        if (body.sessionTimeout && (typeof body.sessionTimeout !== \"number\" || body.sessionTimeout < 5 || body.sessionTimeout > 1440)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Session timeout must be between 5 and 1440 minutes\"\n            }, {\n                status: 400\n            });\n        }\n        if (body.logLevel && ![\n            \"debug\",\n            \"info\",\n            \"warn\",\n            \"error\"\n        ].includes(body.logLevel)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid log level\"\n            }, {\n                status: 400\n            });\n        }\n        if (body.backupFrequency && ![\n            \"hourly\",\n            \"daily\",\n            \"weekly\",\n            \"monthly\"\n        ].includes(body.backupFrequency)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid backup frequency\"\n            }, {\n                status: 400\n            });\n        }\n        // Update system settings\n        systemSettings = {\n            ...systemSettings,\n            ...body,\n            updatedAt: new Date().toISOString()\n        };\n        // Log the update for audit purposes\n        console.log(\"System settings updated:\", {\n            fields: Object.keys(body),\n            timestamp: new Date().toISOString(),\n            updatedBy: \"admin\" // Replace with actual user ID\n        });\n        // Handle special cases\n        if (body.maintenanceMode !== undefined) {\n            console.log(`Maintenance mode ${body.maintenanceMode ? \"enabled\" : \"disabled\"}`);\n        // In production, you might want to notify all active users\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            settings: systemSettings,\n            message: \"System settings updated successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error updating system settings:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update system settings\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const isAdmin = true // Replace with actual admin check\n        ;\n        if (!isAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { action } = body;\n        if (!action) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Action is required\"\n            }, {\n                status: 400\n            });\n        }\n        switch(action){\n            case \"backup\":\n                // Create system backup\n                console.log(\"Creating system backup...\");\n                // Mock backup process\n                const backupId = `backup_${Date.now()}`;\n                const backupResult = {\n                    id: backupId,\n                    status: \"completed\",\n                    size: \"1.2 GB\",\n                    createdAt: new Date().toISOString()\n                };\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    backup: backupResult,\n                    message: \"System backup created successfully\"\n                });\n            case \"clear_cache\":\n                // Clear system cache\n                console.log(\"Clearing system cache...\");\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: \"System cache cleared successfully\"\n                });\n            case \"restart_services\":\n                // Restart system services\n                console.log(\"Restarting system services...\");\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: \"System services restarted successfully\"\n                });\n            case \"export_logs\":\n                // Export system logs\n                console.log(\"Exporting system logs...\");\n                const logExport = {\n                    filename: `logs_${new Date().toISOString().split(\"T\")[0]}.zip`,\n                    size: \"45 MB\",\n                    downloadUrl: \"/api/admin/download/logs\"\n                };\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    export: logExport,\n                    message: \"System logs exported successfully\"\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error processing system action:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to process action\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/settings/system/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fsystem%2Froute&page=%2Fapi%2Fsettings%2Fsystem%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fsystem%2Froute.ts&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();