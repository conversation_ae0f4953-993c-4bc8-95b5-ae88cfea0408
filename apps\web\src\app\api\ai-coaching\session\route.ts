import { NextRequest, NextResponse } from 'next/server'
import { aiCoachingSessionService } from '@/services/aiCoachingSessionService'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { userId, role, sessionType, config, userProfile } = body

    // Validate required fields
    if (!userId || !role || !sessionType) {
      return NextResponse.json(
        { error: 'Missing required fields: userId, role, sessionType' },
        { status: 400 }
      )
    }

    // Create new AI coaching session
    const session = await aiCoachingSessionService.createSession({
      userId,
      role,
      sessionType,
      config: config || {
        difficulty: 5,
        focusAreas: ['General'],
        adaptiveDifficulty: true,
        emotionalAnalysis: true
      },
      userProfile: userProfile || {
        level: 'intermediate',
        experience: [],
        weaknesses: [],
        goals: []
      }
    })

    return NextResponse.json({
      success: true,
      session: {
        id: session.id,
        status: session.status,
        currentQuestion: session.currentQuestion,
        config: session.config,
        startTime: session.startTime
      }
    })
  } catch (error) {
    console.error('Error creating AI coaching session:', error)
    return NextResponse.json(
      { error: 'Failed to create coaching session' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')
    const userId = searchParams.get('userId')

    if (sessionId) {
      // Get specific session
      const session = aiCoachingSessionService.getActiveSession(userId || '')
      if (!session || session.id !== sessionId) {
        return NextResponse.json(
          { error: 'Session not found' },
          { status: 404 }
        )
      }

      return NextResponse.json({
        success: true,
        session: {
          id: session.id,
          status: session.status,
          currentQuestion: session.currentQuestion,
          messages: session.messages,
          performance: session.performance,
          emotionalData: session.emotionalData,
          analytics: session.analytics
        }
      })
    } else if (userId) {
      // Get user's active session
      const session = aiCoachingSessionService.getActiveSession(userId)
      
      return NextResponse.json({
        success: true,
        session: session ? {
          id: session.id,
          status: session.status,
          currentQuestion: session.currentQuestion,
          config: session.config,
          startTime: session.startTime
        } : null
      })
    } else {
      return NextResponse.json(
        { error: 'Missing sessionId or userId parameter' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Error fetching AI coaching session:', error)
    return NextResponse.json(
      { error: 'Failed to fetch coaching session' },
      { status: 500 }
    )
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, action, data } = body

    if (!sessionId || !action) {
      return NextResponse.json(
        { error: 'Missing required fields: sessionId, action' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'pause':
        aiCoachingSessionService.pauseSession(sessionId)
        break
      
      case 'resume':
        aiCoachingSessionService.resumeSession(sessionId)
        break
      
      case 'updateEmotionalState':
        if (!data || !data.emotionalState) {
          return NextResponse.json(
            { error: 'Missing emotional state data' },
            { status: 400 }
          )
        }
        aiCoachingSessionService.updateEmotionalState(sessionId, data.emotionalState)
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error updating AI coaching session:', error)
    return NextResponse.json(
      { error: 'Failed to update coaching session' },
      { status: 500 }
    )
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      )
    }

    aiCoachingSessionService.cancelSession(sessionId)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error cancelling AI coaching session:', error)
    return NextResponse.json(
      { error: 'Failed to cancel coaching session' },
      { status: 500 }
    )
  }
}
