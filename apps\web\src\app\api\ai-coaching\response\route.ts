import { NextRequest, NextResponse } from 'next/server'
import { aiCoachingSessionService } from '@/services/aiCoachingSessionService'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { sessionId, userResponse, responseTime } = body

    // Validate required fields
    if (!sessionId || !userResponse) {
      return NextResponse.json(
        { error: 'Missing required fields: sessionId, userResponse' },
        { status: 400 }
      )
    }

    // Process user response
    const result = await aiCoachingSessionService.processUserResponse(
      sessionId,
      userResponse,
      responseTime || 0
    )

    return NextResponse.json({
      success: true,
      evaluation: result.evaluation,
      nextQuestion: result.nextQuestion,
      sessionComplete: result.sessionComplete,
      progress: aiCoachingSessionService.getSessionProgress(sessionId)
    })
  } catch (error) {
    console.error('Error processing user response:', error)
    
    if (error instanceof Error && error.message.includes('Session')) {
      return NextResponse.json(
        { error: error.message },
        { status: 404 }
      )
    }
    
    return NextResponse.json(
      { error: 'Failed to process user response' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const sessionId = searchParams.get('sessionId')

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing sessionId parameter' },
        { status: 400 }
      )
    }

    // Get session progress
    const progress = aiCoachingSessionService.getSessionProgress(sessionId)

    return NextResponse.json({
      success: true,
      progress
    })
  } catch (error) {
    console.error('Error fetching session progress:', error)
    return NextResponse.json(
      { error: 'Failed to fetch session progress' },
      { status: 500 }
    )
  }
}
