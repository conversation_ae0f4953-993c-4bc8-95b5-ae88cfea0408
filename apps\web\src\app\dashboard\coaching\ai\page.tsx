'use client'

import React, { useState, useEffect, useRef } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'
import { 
  ArrowLeft,
  Brain,
  Mic,
  MicOff,
  Video,
  VideoOff,
  Send,
  Pause,
  Play,
  Square,
  Clock,
  Target,
  TrendingUp,
  Heart,
  Lightbulb,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  MessageCircle,
  Zap
} from 'lucide-react'
import { aiCoachingClient, CreateSessionRequest } from '@/services/aiCoachingClient'
import { AICoachingSession, SessionProgress } from '@/services/aiCoachingSessionService'
import { emotionalIntelligenceService } from '@/services/emotionalIntelligenceService'

export default function AICoachingPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const role = searchParams.get('role') || 'software-engineer'
  
  const [session, setSession] = useState<AICoachingSession | null>(null)
  const [progress, setProgress] = useState<SessionProgress | null>(null)
  const [currentResponse, setCurrentResponse] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [isRecording, setIsRecording] = useState(false)
  const [showHints, setShowHints] = useState(false)
  const [emotionalState, setEmotionalState] = useState({
    confidence: 75,
    stress: 30,
    engagement: 80,
    clarity: 70
  })
  
  const [sessionConfig, setSessionConfig] = useState({
    sessionType: 'practice' as const,
    difficulty: 5,
    focusAreas: ['System Design', 'Algorithms'],
    timeLimit: 3600, // 1 hour
    questionCount: 10,
    adaptiveDifficulty: true,
    emotionalAnalysis: true
  })

  const responseTextareaRef = useRef<HTMLTextAreaElement>(null)
  const mediaStreamRef = useRef<MediaStream | null>(null)

  useEffect(() => {
    initializeSession()
    return () => {
      // Cleanup on unmount
      if (mediaStreamRef.current) {
        mediaStreamRef.current.getTracks().forEach(track => track.stop())
      }
    }
  }, [role])

  useEffect(() => {
    if (session) {
      const interval = setInterval(() => {
        updateProgress()
      }, 1000)
      return () => clearInterval(interval)
    }
  }, [session])

  const initializeSession = async () => {
    try {
      setIsLoading(true)

      const createRequest: CreateSessionRequest = {
        userId: 'current-user', // Replace with actual user ID
        role,
        sessionType: sessionConfig.sessionType,
        config: sessionConfig,
        userProfile: {
          level: 'intermediate',
          experience: ['JavaScript', 'React', 'Node.js'],
          weaknesses: ['System Design', 'Algorithms'],
          goals: ['Pass technical interviews', 'Improve problem-solving skills']
        }
      }

      const result = await aiCoachingClient.createSession(createRequest)

      if (result.success && result.session) {
        setSession(result.session as AICoachingSession)
        updateProgress()

        // Start media capture for emotional analysis
        if (sessionConfig.emotionalAnalysis) {
          await startMediaCapture()
        }
      } else {
        console.error('Failed to create session:', result.error)
      }
    } catch (error) {
      console.error('Error initializing session:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateProgress = async () => {
    if (session?.id) {
      const result = await aiCoachingClient.getSessionProgress(session.id)
      if (result.success && result.progress) {
        setProgress(result.progress)
      }
    }
  }

  const startMediaCapture = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      })
      mediaStreamRef.current = stream
      setIsRecording(true)
      
      // Start periodic emotional analysis
      startEmotionalAnalysis()
    } catch (error) {
      console.error('Error accessing media devices:', error)
    }
  }

  const startEmotionalAnalysis = () => {
    setInterval(async () => {
      if (!session || !mediaStreamRef.current) return

      try {
        // Capture audio/video snippets for analysis
        const audioBlob = await captureAudioSnippet()
        const videoBlob = await captureVideoSnippet()
        
        // Process emotional data
        const analysis = await emotionalIntelligenceService.processRealTimeEmotionalData(
          session.id,
          audioBlob,
          videoBlob
        )
        
        // Update emotional state
        const newEmotionalState = {
          confidence: Math.round(analysis.overallState.confidence * 100),
          stress: Math.round(analysis.overallState.stress * 100),
          engagement: Math.round(analysis.overallState.engagement * 100),
          clarity: Math.round(analysis.overallState.clarity * 100)
        }
        
        setEmotionalState(newEmotionalState)
        
        // Update session with emotional data
        await aiCoachingClient.updateEmotionalState(session.id, {
          confidence: analysis.overallState.confidence,
          stress: analysis.overallState.stress,
          engagement: analysis.overallState.engagement,
          clarity: analysis.overallState.clarity
        })
      } catch (error) {
        console.error('Error in emotional analysis:', error)
      }
    }, 5000) // Analyze every 5 seconds
  }

  const submitResponse = async () => {
    if (!session?.id || !currentResponse.trim()) return

    try {
      setIsLoading(true)
      const responseTime = Date.now() - (session.currentQuestion?.timestamp || Date.now())

      const result = await aiCoachingClient.processResponse({
        sessionId: session.id,
        userResponse: currentResponse,
        responseTime
      })

      if (result.success && result.result) {
        // Update session with new question
        if (result.result.nextQuestion) {
          setSession(prev => prev ? {
            ...prev,
            currentQuestion: result.result!.nextQuestion,
            messages: [...prev.messages, {
              role: 'user' as const,
              content: currentResponse,
              timestamp: Date.now()
            }, {
              role: 'assistant' as const,
              content: result.result!.nextQuestion!.message,
              timestamp: Date.now()
            }]
          } : null)
        }

        // Update progress
        setProgress(result.result.progress)

        // Clear response
        setCurrentResponse('')

        // Check if session is complete
        if (result.result.sessionComplete) {
          handleSessionComplete()
        }
      } else {
        console.error('Failed to process response:', result.error)
      }
    } catch (error) {
      console.error('Error submitting response:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSessionComplete = () => {
    // Navigate to results page
    router.push(`/dashboard/coaching/results?sessionId=${session?.id}`)
  }

  const pauseSession = async () => {
    if (session?.id) {
      const result = await aiCoachingClient.pauseSession(session.id)
      if (result.success) {
        setSession(prev => prev ? { ...prev, status: 'paused' } : null)
      }
    }
  }

  const resumeSession = async () => {
    if (session?.id) {
      const result = await aiCoachingClient.resumeSession(session.id)
      if (result.success) {
        setSession(prev => prev ? { ...prev, status: 'active' } : null)
      }
    }
  }

  const endSession = async () => {
    if (session?.id) {
      await aiCoachingClient.cancelSession(session.id)
      router.push('/dashboard/experts')
    }
  }

  // Mock functions for media capture
  const captureAudioSnippet = async (): Promise<Blob> => {
    return new Blob(['mock audio'], { type: 'audio/wav' })
  }

  const captureVideoSnippet = async (): Promise<Blob> => {
    return new Blob(['mock video'], { type: 'video/mp4' })
  }

  const getEmotionalColor = (value: number, isStress = false) => {
    if (isStress) {
      if (value > 70) return 'text-red-600'
      if (value > 40) return 'text-yellow-600'
      return 'text-green-600'
    } else {
      if (value > 80) return 'text-green-600'
      if (value > 60) return 'text-yellow-600'
      return 'text-red-600'
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (isLoading && !session) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <Brain className="h-12 w-12 text-blue-600 animate-pulse mx-auto mb-4" />
          <p className="text-lg font-medium">Initializing AI Coach...</p>
          <p className="text-gray-600">Setting up your personalized coaching session</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <Button 
              variant="outline" 
              onClick={() => router.push('/dashboard/experts')}
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Experts
            </Button>
            
            <div className="flex items-center space-x-2">
              <Brain className="h-6 w-6 text-blue-600" />
              <h1 className="text-2xl font-bold text-gray-900">
                AI Coach - {role.replace('-', ' ').toUpperCase()}
              </h1>
              {session?.status === 'active' && (
                <Badge variant="outline" className="ml-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse" />
                  Live
                </Badge>
              )}
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {session?.status === 'active' ? (
              <Button onClick={pauseSession} variant="outline" size="sm">
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </Button>
            ) : (
              <Button onClick={resumeSession} variant="outline" size="sm">
                <Play className="h-4 w-4 mr-2" />
                Resume
              </Button>
            )}
            
            <Button onClick={endSession} variant="destructive" size="sm">
              <Square className="h-4 w-4 mr-2" />
              End Session
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Main Coaching Area */}
          <div className="lg:col-span-3 space-y-6">
            {/* Progress Bar */}
            {progress && (
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Session Progress</span>
                    <span className="text-sm text-gray-600">
                      {progress.currentQuestionIndex} / {progress.totalQuestions} questions
                    </span>
                  </div>
                  <Progress value={progress.completionPercentage} className="mb-2" />
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>Time: {formatTime(progress.timeElapsed)}</span>
                    <span>Avg Score: {progress.averageScore}%</span>
                    <span>Difficulty: {progress.currentDifficulty}/10</span>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Current Question */}
            {session?.currentQuestion && (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center space-x-2">
                      <MessageCircle className="h-5 w-5 text-blue-600" />
                      <span>Current Question</span>
                    </CardTitle>
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">
                        {session.currentQuestion.questionType}
                      </Badge>
                      <Badge variant="outline">
                        Difficulty: {session.currentQuestion.difficulty}/10
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <p className="text-lg leading-relaxed">
                      {session.currentQuestion.message}
                    </p>
                    
                    {session.currentQuestion.category && (
                      <div className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-gray-500" />
                        <span className="text-sm text-gray-600">
                          Category: {session.currentQuestion.category}
                        </span>
                      </div>
                    )}
                    
                    {showHints && session.currentQuestion.hints && session.currentQuestion.hints.length > 0 && (
                      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <Lightbulb className="h-4 w-4 text-yellow-600" />
                          <span className="text-sm font-medium text-yellow-800">Hints</span>
                        </div>
                        <ul className="text-sm text-yellow-700 space-y-1">
                          {session.currentQuestion.hints.map((hint, index) => (
                            <li key={index}>• {hint}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Response Area */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Send className="h-5 w-5 text-green-600" />
                  <span>Your Response</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <Textarea
                    ref={responseTextareaRef}
                    value={currentResponse}
                    onChange={(e) => setCurrentResponse(e.target.value)}
                    placeholder="Type your response here... Be specific and explain your thinking process."
                    rows={6}
                    className="resize-none"
                  />
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowHints(!showHints)}
                      >
                        <Lightbulb className="h-4 w-4 mr-2" />
                        {showHints ? 'Hide Hints' : 'Show Hints'}
                      </Button>
                      
                      <Button variant="outline" size="sm">
                        <Mic className="h-4 w-4 mr-2" />
                        Voice Input
                      </Button>
                    </div>
                    
                    <Button 
                      onClick={submitResponse}
                      disabled={!currentResponse.trim() || isLoading}
                      className="bg-blue-600 hover:bg-blue-700"
                    >
                      {isLoading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Processing...
                        </>
                      ) : (
                        <>
                          <Send className="h-4 w-4 mr-2" />
                          Submit Response
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Emotional Intelligence Metrics */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Heart className="h-5 w-5 text-red-500" />
                  <span>Live Metrics</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Confidence</span>
                    <span className={`text-sm font-bold ${getEmotionalColor(emotionalState.confidence)}`}>
                      {emotionalState.confidence}%
                    </span>
                  </div>
                  <Progress value={emotionalState.confidence} className="h-2" />
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Stress Level</span>
                    <span className={`text-sm font-bold ${getEmotionalColor(emotionalState.stress, true)}`}>
                      {emotionalState.stress}%
                    </span>
                  </div>
                  <Progress value={emotionalState.stress} className="h-2" />
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Engagement</span>
                    <span className={`text-sm font-bold ${getEmotionalColor(emotionalState.engagement)}`}>
                      {emotionalState.engagement}%
                    </span>
                  </div>
                  <Progress value={emotionalState.engagement} className="h-2" />
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Clarity</span>
                    <span className={`text-sm font-bold ${getEmotionalColor(emotionalState.clarity)}`}>
                      {emotionalState.clarity}%
                    </span>
                  </div>
                  <Progress value={emotionalState.clarity} className="h-2" />
                </div>
              </CardContent>
            </Card>

            {/* Session Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-blue-500" />
                  <span>Session Stats</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {progress && (
                  <>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Questions Answered</span>
                      <span className="font-medium">{progress.currentQuestionIndex}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Average Score</span>
                      <span className="font-medium">{progress.averageScore}%</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Time Elapsed</span>
                      <span className="font-medium">{formatTime(progress.timeElapsed)}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Current Difficulty</span>
                      <span className="font-medium">{progress.currentDifficulty}/10</span>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Media Controls */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Video className="h-5 w-5 text-purple-500" />
                  <span>Media</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Camera</span>
                  <Button variant="outline" size="sm">
                    {isRecording ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Microphone</span>
                  <Button variant="outline" size="sm">
                    {isRecording ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                  </Button>
                </div>
                <div className="text-xs text-gray-500">
                  {isRecording ? 'Recording for emotional analysis' : 'Media access disabled'}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
