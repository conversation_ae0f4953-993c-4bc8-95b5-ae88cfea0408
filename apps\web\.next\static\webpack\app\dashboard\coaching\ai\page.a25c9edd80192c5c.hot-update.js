"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/coaching/ai/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/coaching/ai/page.tsx":
/*!************************************************!*\
  !*** ./src/app/dashboard/coaching/ai/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AICoachingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/aiCoachingClient */ \"(app-pages-browser)/./src/services/aiCoachingClient.ts\");\n/* harmony import */ var _services_emotionalIntelligenceService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/emotionalIntelligenceService */ \"(app-pages-browser)/./src/services/emotionalIntelligenceService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AICoachingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const role = searchParams.get(\"role\") || \"software-engineer\";\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentResponse, setCurrentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [emotionalState, setEmotionalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        confidence: 75,\n        stress: 30,\n        engagement: 80,\n        clarity: 70\n    });\n    const [sessionConfig, setSessionConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        sessionType: \"practice\",\n        difficulty: 5,\n        focusAreas: [\n            \"System Design\",\n            \"Algorithms\"\n        ],\n        timeLimit: 3600,\n        questionCount: 10,\n        adaptiveDifficulty: true,\n        emotionalAnalysis: true\n    });\n    const responseTextareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mediaStreamRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeSession();\n        return ()=>{\n            // Cleanup on unmount\n            if (mediaStreamRef.current) {\n                mediaStreamRef.current.getTracks().forEach((track)=>track.stop());\n            }\n        };\n    }, [\n        role\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            // Update progress once when session is created\n            updateProgress();\n            // Set up periodic updates only if session is active\n            if (session.status === \"active\") {\n                const interval = setInterval(()=>{\n                    updateProgress();\n                }, 5000) // Update every 5 seconds instead of 1 second\n                ;\n                return ()=>clearInterval(interval);\n            }\n        }\n    }, [\n        session === null || session === void 0 ? void 0 : session.id,\n        session === null || session === void 0 ? void 0 : session.status\n    ]);\n    const initializeSession = async ()=>{\n        try {\n            setIsLoading(true);\n            const createRequest = {\n                userId: \"current-user\",\n                role,\n                sessionType: sessionConfig.sessionType,\n                config: sessionConfig,\n                userProfile: {\n                    level: \"intermediate\",\n                    experience: [\n                        \"JavaScript\",\n                        \"React\",\n                        \"Node.js\"\n                    ],\n                    weaknesses: [\n                        \"System Design\",\n                        \"Algorithms\"\n                    ],\n                    goals: [\n                        \"Pass technical interviews\",\n                        \"Improve problem-solving skills\"\n                    ]\n                }\n            };\n            const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.createSession(createRequest);\n            if (result.success && result.session) {\n                const newSession = result.session;\n                setSession(newSession);\n                // Start media capture for emotional analysis\n                if (sessionConfig.emotionalAnalysis) {\n                    await startMediaCapture();\n                }\n            } else {\n                console.error(\"Failed to create session:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"Error initializing session:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateProgress = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            try {\n                const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.getSessionProgress(session.id);\n                if (result.success && result.progress) {\n                    setProgress(result.progress);\n                } else {\n                    console.warn(\"Failed to get session progress:\", result.error);\n                }\n            } catch (error) {\n                console.error(\"Error updating progress:\", error);\n            }\n        }\n    };\n    const startMediaCapture = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true,\n                audio: true\n            });\n            mediaStreamRef.current = stream;\n            setIsRecording(true);\n            // Start periodic emotional analysis\n            startEmotionalAnalysis();\n        } catch (error) {\n            console.error(\"Error accessing media devices:\", error);\n        }\n    };\n    const startEmotionalAnalysis = ()=>{\n        setInterval(async ()=>{\n            if (!session || !mediaStreamRef.current) return;\n            try {\n                // Capture audio/video snippets for analysis\n                const audioBlob = await captureAudioSnippet();\n                const videoBlob = await captureVideoSnippet();\n                // Process emotional data\n                const analysis = await _services_emotionalIntelligenceService__WEBPACK_IMPORTED_MODULE_9__.emotionalIntelligenceService.processRealTimeEmotionalData(session.id, audioBlob, videoBlob);\n                // Update emotional state\n                const newEmotionalState = {\n                    confidence: Math.round(analysis.overallState.confidence * 100),\n                    stress: Math.round(analysis.overallState.stress * 100),\n                    engagement: Math.round(analysis.overallState.engagement * 100),\n                    clarity: Math.round(analysis.overallState.clarity * 100)\n                };\n                setEmotionalState(newEmotionalState);\n                // Update session with emotional data\n                await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.updateEmotionalState(session.id, {\n                    confidence: analysis.overallState.confidence,\n                    stress: analysis.overallState.stress,\n                    engagement: analysis.overallState.engagement,\n                    clarity: analysis.overallState.clarity\n                });\n            } catch (error) {\n                console.error(\"Error in emotional analysis:\", error);\n            }\n        }, 5000) // Analyze every 5 seconds\n        ;\n    };\n    const submitResponse = async ()=>{\n        if (!(session === null || session === void 0 ? void 0 : session.id) || !currentResponse.trim()) return;\n        try {\n            var _session_currentQuestion;\n            setIsLoading(true);\n            const responseTime = Date.now() - (((_session_currentQuestion = session.currentQuestion) === null || _session_currentQuestion === void 0 ? void 0 : _session_currentQuestion.timestamp) || Date.now());\n            const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.processResponse({\n                sessionId: session.id,\n                userResponse: currentResponse,\n                responseTime\n            });\n            if (result.success && result.result) {\n                // Update session with new question\n                if (result.result.nextQuestion) {\n                    setSession((prev)=>prev ? {\n                            ...prev,\n                            currentQuestion: result.result.nextQuestion,\n                            messages: [\n                                ...prev.messages,\n                                {\n                                    role: \"user\",\n                                    content: currentResponse,\n                                    timestamp: Date.now()\n                                },\n                                {\n                                    role: \"assistant\",\n                                    content: result.result.nextQuestion.message,\n                                    timestamp: Date.now()\n                                }\n                            ]\n                        } : null);\n                }\n                // Update progress\n                setProgress(result.result.progress);\n                // Clear response\n                setCurrentResponse(\"\");\n                // Check if session is complete\n                if (result.result.sessionComplete) {\n                    handleSessionComplete();\n                }\n            } else {\n                console.error(\"Failed to process response:\", result.error);\n            }\n        } catch (error) {\n            console.error(\"Error submitting response:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSessionComplete = ()=>{\n        // Navigate to results page\n        router.push(\"/dashboard/coaching/results?sessionId=\".concat(session === null || session === void 0 ? void 0 : session.id));\n    };\n    const pauseSession = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.pauseSession(session.id);\n            if (result.success) {\n                setSession((prev)=>prev ? {\n                        ...prev,\n                        status: \"paused\"\n                    } : null);\n            }\n        }\n    };\n    const resumeSession = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.resumeSession(session.id);\n            if (result.success) {\n                setSession((prev)=>prev ? {\n                        ...prev,\n                        status: \"active\"\n                    } : null);\n            }\n        }\n    };\n    const endSession = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.cancelSession(session.id);\n            router.push(\"/dashboard/experts\");\n        }\n    };\n    // Mock functions for media capture\n    const captureAudioSnippet = async ()=>{\n        return new Blob([\n            \"mock audio\"\n        ], {\n            type: \"audio/wav\"\n        });\n    };\n    const captureVideoSnippet = async ()=>{\n        return new Blob([\n            \"mock video\"\n        ], {\n            type: \"video/mp4\"\n        });\n    };\n    const getEmotionalColor = function(value) {\n        let isStress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (isStress) {\n            if (value > 70) return \"text-red-600\";\n            if (value > 40) return \"text-yellow-600\";\n            return \"text-green-600\";\n        } else {\n            if (value > 80) return \"text-green-600\";\n            if (value > 60) return \"text-yellow-600\";\n            return \"text-red-600\";\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, \"0\"));\n    };\n    if (isLoading && !session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-blue-600 animate-pulse mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Initializing AI Coach...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Setting up your personalized coaching session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                        lineNumber: 313,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                lineNumber: 310,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n            lineNumber: 309,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>router.push(\"/dashboard/experts\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Experts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: [\n                                                \"AI Coach - \",\n                                                role.replace(\"-\", \" \").toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        (session === null || session === void 0 ? void 0 : session.status) === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Live\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                (session === null || session === void 0 ? void 0 : session.status) === \"active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: pauseSession,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Pause\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 349,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: resumeSession,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Resume\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: endSession,\n                                    variant: \"destructive\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"End Session\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                    lineNumber: 323,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 space-y-6\",\n                            children: [\n                                progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Session Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            progress.currentQuestionIndex,\n                                                            \" / \",\n                                                            progress.totalQuestions,\n                                                            \" questions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 374,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                value: progress.completionPercentage,\n                                                className: \"mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 380,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Time: \",\n                                                            formatTime(progress.timeElapsed)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Avg Score: \",\n                                                            progress.averageScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Difficulty: \",\n                                                            progress.currentDifficulty,\n                                                            \"/10\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 384,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 381,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                (session === null || session === void 0 ? void 0 : session.currentQuestion) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 396,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Current Question\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 397,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: session.currentQuestion.questionType\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 400,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: [\n                                                                    \"Difficulty: \",\n                                                                    session.currentQuestion.difficulty,\n                                                                    \"/10\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 403,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 399,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg leading-relaxed\",\n                                                        children: session.currentQuestion.message\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 411,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    session.currentQuestion.category && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    \"Category: \",\n                                                                    session.currentQuestion.category\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 416,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    showHints && session.currentQuestion.hints && session.currentQuestion.hints.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 427,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-yellow-800\",\n                                                                        children: \"Hints\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 428,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-yellow-700 space-y-1\",\n                                                                children: session.currentQuestion.hints.map((hint, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: [\n                                                                            \"• \",\n                                                                            hint\n                                                                        ]\n                                                                    }, index, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 432,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 430,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 425,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 446,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Your Response\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 447,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                        ref: responseTextareaRef,\n                                                        value: currentResponse,\n                                                        onChange: (e)=>setCurrentResponse(e.target.value),\n                                                        placeholder: \"Type your response here... Be specific and explain your thinking process.\",\n                                                        rows: 6,\n                                                        className: \"resize-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 452,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>setShowHints(!showHints),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                                lineNumber: 468,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            showHints ? \"Hide Hints\" : \"Show Hints\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                                lineNumber: 473,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Voice Input\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 472,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: submitResponse,\n                                                                disabled: !currentResponse.trim() || isLoading,\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                            lineNumber: 485,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Processing...\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                            lineNumber: 490,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Submit Response\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 451,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 450,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 443,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Live Metrics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 508,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 506,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 505,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Confidence\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.confidence)),\n                                                                    children: [\n                                                                        emotionalState.confidence,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 513,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.confidence,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 519,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Stress Level\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 524,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.stress, true)),\n                                                                    children: [\n                                                                        emotionalState.stress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 525,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 523,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.stress,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 529,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Engagement\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.engagement)),\n                                                                    children: [\n                                                                        emotionalState.engagement,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.engagement,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Clarity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.clarity)),\n                                                                    children: [\n                                                                        emotionalState.clarity,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.clarity,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 558,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Session Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 557,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 556,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Questions Answered\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 566,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: progress.currentQuestionIndex\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 567,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 565,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Average Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    progress.averageScore,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 571,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Time Elapsed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 574,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: formatTime(progress.timeElapsed)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 575,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Current Difficulty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 578,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    progress.currentDifficulty,\n                                                                    \"/10\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 579,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 577,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 555,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 text-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 590,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Media\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 591,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 589,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 596,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 36\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 68\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 597,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 595,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Microphone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 602,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 36\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 604,\n                                                                columnNumber: 66\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 603,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 601,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: isRecording ? \"Recording for emotional analysis\" : \"Media access disabled\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 594,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 587,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 502,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n            lineNumber: 321,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n        lineNumber: 320,\n        columnNumber: 5\n    }, this);\n}\n_s(AICoachingPage, \"CrZXjNRejucqZSi37/vHtdwm/xY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AICoachingPage;\nvar _c;\n$RefreshReg$(_c, \"AICoachingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/coaching/ai/page.tsx\n"));

/***/ })

});