"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/resume/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/resume/page.tsx":
/*!*******************************************!*\
  !*** ./src/app/dashboard/resume/page.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ ResumePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,BarChart3,CheckCircle,Clock,Download,Edit,Eye,FileText,Plus,Star,Target,Trash2,TrendingUp,Upload,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction ResumePage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [resumes, setResumes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [selectedResume, setSelectedResume] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Load user's resumes\n        loadResumes();\n    }, []);\n    const loadResumes = async ()=>{\n        try {\n            setIsLoading(true);\n            // TODO: Implement API call to load resumes\n            // const response = await apiClient.getResumes()\n            // setResumes(response)\n            // Mock data for now - will be replaced with real API call\n            setResumes([\n                {\n                    id: \"1\",\n                    name: \"Software Engineer Resume\",\n                    fileName: \"john_doe_resume.pdf\",\n                    uploadedAt: new Date(\"2024-01-15\"),\n                    atsScore: 85,\n                    status: \"analyzed\",\n                    fileSize: \"245 KB\",\n                    keywords: [\n                        \"JavaScript\",\n                        \"React\",\n                        \"Node.js\",\n                        \"Python\"\n                    ],\n                    suggestions: 3\n                },\n                {\n                    id: \"2\",\n                    name: \"Senior Developer Resume\",\n                    fileName: \"john_doe_senior.pdf\",\n                    uploadedAt: new Date(\"2024-01-10\"),\n                    atsScore: 72,\n                    status: \"needs_improvement\",\n                    fileSize: \"198 KB\",\n                    keywords: [\n                        \"Java\",\n                        \"Spring\",\n                        \"AWS\",\n                        \"Docker\"\n                    ],\n                    suggestions: 7\n                }\n            ]);\n        } catch (error) {\n            console.error(\"Failed to load resumes:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 80) return \"text-green-600\";\n        if (score >= 60) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    const getScoreBadgeVariant = (score)=>{\n        if (score >= 80) return \"default\";\n        if (score >= 60) return \"secondary\";\n        return \"destructive\";\n    };\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"analyzed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"h-4 w-4 text-green-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 16\n                }, this);\n            case \"needs_improvement\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"h-4 w-4 text-yellow-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 16\n                }, this);\n            case \"processing\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                    className: \"h-4 w-4 text-blue-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    className: \"h-4 w-4 text-gray-600\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-600\",\n                                children: \"Resume Manager\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 mt-2\",\n                                children: \"Upload, analyze, and optimize your resumes for ATS systems\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: ()=>router.push(\"/dashboard/resume/builder\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Create Resume\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: ()=>router.push(\"/dashboard/resume/upload\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 119,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Upload Resume\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                lineNumber: 106,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                defaultValue: \"overview\",\n                className: \"space-y-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"overview\",\n                                children: \"Overview\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"resumes\",\n                                children: \"My Resumes\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"analytics\",\n                                children: \"Analytics\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                value: \"optimization\",\n                                children: \"ATS Optimization\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 130,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"overview\",\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Total Resumes\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 138,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 139,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 137,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: resumes.length\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 142,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"+1 from last month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 143,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 141,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Average ATS Score\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: \"78.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 154,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"+5.2% from last month\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 148,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Optimizations\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: \"10\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Suggestions applied\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 167,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Success Rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"h-4 w-4 text-muted-foreground\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: \"92%\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: \"Interview callback rate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 135,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                children: \"Recent Activity\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                children: \"Your latest resume uploads and optimizations\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: resumes.slice(0, 3).map((resume)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: getStatusIcon(resume.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-medium text-gray-600 truncate\",\n                                                                    children: resume.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 202,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"Uploaded \",\n                                                                        resume.uploadedAt.toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 205,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: getScoreBadgeVariant(resume.atsScore),\n                                                                children: [\n                                                                    \"ATS: \",\n                                                                    resume.atsScore,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, resume.id, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"resumes\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                            children: [\n                                resumes.map((resume)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        className: \"hover:shadow-lg transition-shadow\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                                className: \"text-lg\",\n                                                                children: resume.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            getStatusIcon(resume.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                        children: resume.fileName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 231,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 226,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: \"ATS Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 235,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg font-bold \".concat(getScoreColor(resume.atsScore)),\n                                                                children: [\n                                                                    resume.atsScore,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 234,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_6__.Progress, {\n                                                        value: resume.atsScore,\n                                                        className: \"h-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-wrap gap-1\",\n                                                        children: [\n                                                            resume.keywords.slice(0, 3).map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: \"secondary\",\n                                                                    className: \"text-xs\",\n                                                                    children: keyword\n                                                                }, index, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 244,\n                                                                    columnNumber: 23\n                                                                }, this)),\n                                                            resume.keywords.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                className: \"text-xs\",\n                                                                children: [\n                                                                    \"+\",\n                                                                    resume.keywords.length - 3,\n                                                                    \" more\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between text-sm text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: resume.fileSize\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 256,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    resume.suggestions,\n                                                                    \" suggestions\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                        lineNumber: 262,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"View\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"mr-1 h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                        lineNumber: 266,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    \"Edit\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 265,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 270,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 269,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                    lineNumber: 273,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                                lineNumber: 272,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, resume.id, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"border-dashed border-2 hover:border-blue-500 transition-colors cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"flex flex-col items-center justify-center h-full p-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-12 w-12 text-gray-400 mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-600 mb-2\",\n                                                children: \"Upload New Resume\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500 text-center mb-4\",\n                                                children: \"Drag and drop your resume or click to browse\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>router.push(\"/dashboard/resume/upload\"),\n                                                children: \"Choose File\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                    lineNumber: 281,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                            lineNumber: 223,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"analytics\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Resume Analytics\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Detailed insights into your resume performance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"Analytics Dashboard Coming Soon\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500\",\n                                                children: \"We're building comprehensive analytics to help you track your resume performance.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                            lineNumber: 297,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 296,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                        value: \"optimization\",\n                        className: \"space-y-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"ATS Optimization\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                            lineNumber: 321,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"Improve your resume's compatibility with Applicant Tracking Systems\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                    lineNumber: 320,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_BarChart3_CheckCircle_Clock_Download_Edit_Eye_FileText_Plus_Star_Target_Trash2_TrendingUp_Upload_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-16 w-16 text-gray-400 mx-auto mb-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 328,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 mb-2\",\n                                                children: \"ATS Optimization Engine Coming Soon\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 329,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-500\",\n                                                children: \"Our AI will analyze your resume and provide specific recommendations to improve ATS compatibility.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                        lineNumber: 318,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\resume\\\\page.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, this);\n}\n_s(ResumePage, \"mku4Hi+EWsDiK68U1j353Ei/xmY=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ResumePage;\nvar _c;\n$RefreshReg$(_c, \"ResumePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/resume/page.tsx\n"));

/***/ })

});