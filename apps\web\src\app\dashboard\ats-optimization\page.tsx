'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Switch } from '@/components/ui/switch'
import {
  FileText,
  Upload,
  Scan,
  Target,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Eye,
  Download,
  Zap,
  Brain,
  Search,
  Filter,
  BarChart3,
  Settings,
  Lightbulb,
  Award,
  Clock,
  Users,
  Star,
  Plus
} from 'lucide-react'

interface ATSScore {
  overall: number
  formatting: number
  keywords: number
  structure: number
  readability: number
}

interface OptimizationSuggestion {
  id: string
  type: 'critical' | 'important' | 'minor'
  category: 'keywords' | 'formatting' | 'structure' | 'content'
  title: string
  description: string
  impact: 'high' | 'medium' | 'low'
  effort: 'easy' | 'moderate' | 'complex'
  before?: string
  after?: string
}

interface ResumeAnalysis {
  id: string
  fileName: string
  uploadDate: string
  atsScore: ATSScore
  suggestions: OptimizationSuggestion[]
  keywords: {
    found: string[]
    missing: string[]
    density: Record<string, number>
  }
  jobMatch: number
  status: 'analyzing' | 'completed' | 'error'
}

export default function ATSOptimizationPage() {
  const [activeTab, setActiveTab] = useState('scanner')
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [currentAnalysis, setCurrentAnalysis] = useState<ResumeAnalysis | null>(null)
  const [analysisHistory, setAnalysisHistory] = useState<ResumeAnalysis[]>([])
  const [selectedJobDescription, setSelectedJobDescription] = useState('')

  // Mock data for demonstration
  const mockAnalysis: ResumeAnalysis = {
    id: '1',
    fileName: 'john_doe_resume.pdf',
    uploadDate: '2024-01-20T10:30:00Z',
    atsScore: {
      overall: 78,
      formatting: 85,
      keywords: 72,
      structure: 80,
      readability: 76
    },
    suggestions: [
      {
        id: '1',
        type: 'critical',
        category: 'keywords',
        title: 'Missing Key Technical Skills',
        description: 'Your resume lacks important keywords that appear in the job description',
        impact: 'high',
        effort: 'easy',
        before: 'Developed web applications',
        after: 'Developed React.js web applications using TypeScript and Node.js'
      },
      {
        id: '2',
        type: 'important',
        category: 'formatting',
        title: 'Use Standard Section Headers',
        description: 'ATS systems prefer standard section headers like "Work Experience" over creative alternatives',
        impact: 'medium',
        effort: 'easy',
        before: 'Professional Journey',
        after: 'Work Experience'
      },
      {
        id: '3',
        type: 'minor',
        category: 'structure',
        title: 'Add Skills Section',
        description: 'Include a dedicated skills section for better keyword recognition',
        impact: 'medium',
        effort: 'moderate'
      }
    ],
    keywords: {
      found: ['JavaScript', 'React', 'Project Management', 'Team Leadership'],
      missing: ['TypeScript', 'Node.js', 'AWS', 'Agile', 'Scrum'],
      density: {
        'JavaScript': 3.2,
        'React': 2.8,
        'Project Management': 1.5,
        'Team Leadership': 1.2
      }
    },
    jobMatch: 72,
    status: 'completed'
  }

  useEffect(() => {
    // Simulate loading analysis
    if (analysisHistory.length === 0) {
      setAnalysisHistory([mockAnalysis])
      setCurrentAnalysis(mockAnalysis)
    }
  }, [])

  const handleFileUpload = async (file: File) => {
    setIsAnalyzing(true)
    
    // Simulate analysis process
    setTimeout(() => {
      setCurrentAnalysis(mockAnalysis)
      setIsAnalyzing(false)
    }, 3000)
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600 dark:text-green-400'
    if (score >= 60) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-100 dark:bg-green-950/20'
    if (score >= 60) return 'bg-yellow-100 dark:bg-yellow-950/20'
    return 'bg-red-100 dark:bg-red-950/20'
  }

  const getSuggestionIcon = (type: string) => {
    switch (type) {
      case 'critical': return <XCircle className="h-4 w-4 text-red-500" />
      case 'important': return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'minor': return <Lightbulb className="h-4 w-4 text-blue-500" />
      default: return <CheckCircle className="h-4 w-4 text-green-500" />
    }
  }

  const getSuggestionBadgeColor = (type: string) => {
    switch (type) {
      case 'critical': return 'bg-red-100 dark:bg-red-950/20 text-red-800 dark:text-red-400 border-red-200 dark:border-red-800'
      case 'important': return 'bg-yellow-100 dark:bg-yellow-950/20 text-yellow-800 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800'
      case 'minor': return 'bg-blue-100 dark:bg-blue-950/20 text-blue-800 dark:text-blue-400 border-blue-200 dark:border-blue-800'
      default: return 'bg-green-100 dark:bg-green-950/20 text-green-800 dark:text-green-400 border-green-200 dark:border-green-800'
    }
  }

  return (
    <div className="min-h-screen bg-background py-8">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center space-x-2">
              <Target className="h-8 w-8 text-primary" />
              <span>ATS Optimization</span>
            </h1>
            <p className="text-muted-foreground mt-2">
              Improve your resume's compatibility with Applicant Tracking Systems using AI
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <Button variant="outline">
              <Eye className="mr-2 h-4 w-4" />
              View History
            </Button>
            <Button className="bg-primary hover:bg-primary/90">
              <Upload className="mr-2 h-4 w-4" />
              Upload Resume
            </Button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Target className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {currentAnalysis?.atsScore.overall || 0}%
                  </p>
                  <p className="text-sm text-muted-foreground">ATS Score</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Search className="h-8 w-8 text-green-600 dark:text-green-400" />
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {currentAnalysis?.keywords.found.length || 0}
                  </p>
                  <p className="text-sm text-muted-foreground">Keywords Found</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-8 w-8 text-purple-600 dark:text-purple-400" />
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {currentAnalysis?.jobMatch || 0}%
                  </p>
                  <p className="text-sm text-muted-foreground">Job Match</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-2">
                <Lightbulb className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                <div>
                  <p className="text-2xl font-bold text-foreground">
                    {currentAnalysis?.suggestions.length || 0}
                  </p>
                  <p className="text-sm text-muted-foreground">Suggestions</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="scanner" className="flex items-center space-x-2">
              <Scan className="h-4 w-4" />
              <span>Scanner</span>
            </TabsTrigger>
            <TabsTrigger value="analysis" className="flex items-center space-x-2">
              <BarChart3 className="h-4 w-4" />
              <span>Analysis</span>
            </TabsTrigger>
            <TabsTrigger value="keywords" className="flex items-center space-x-2">
              <Search className="h-4 w-4" />
              <span>Keywords</span>
            </TabsTrigger>
            <TabsTrigger value="suggestions" className="flex items-center space-x-2">
              <Lightbulb className="h-4 w-4" />
              <span>Suggestions</span>
            </TabsTrigger>
            <TabsTrigger value="templates" className="flex items-center space-x-2">
              <FileText className="h-4 w-4" />
              <span>Templates</span>
            </TabsTrigger>
          </TabsList>

          {/* Scanner Tab */}
          <TabsContent value="scanner" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Scan className="h-5 w-5 text-primary" />
                  <span>Resume Scanner</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isAnalyzing ? (
                  <div className="text-center py-12">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                    <h3 className="text-lg font-medium text-foreground mb-2">Analyzing Your Resume</h3>
                    <p className="text-muted-foreground">
                      Our AI is scanning your resume for ATS compatibility...
                    </p>
                    <div className="mt-4 max-w-md mx-auto">
                      <Progress value={65} className="h-2" />
                      <p className="text-sm text-muted-foreground mt-2">Analyzing keywords and formatting...</p>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* Upload Area */}
                    <div className="border-2 border-dashed border-border rounded-lg p-8 text-center">
                      <Upload className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-foreground mb-2">Upload Your Resume</h3>
                      <p className="text-muted-foreground mb-4">
                        Drag and drop your resume or click to browse
                      </p>
                      <div className="flex items-center justify-center space-x-4">
                        <Button 
                          onClick={() => {
                            const input = document.createElement('input')
                            input.type = 'file'
                            input.accept = '.pdf,.doc,.docx'
                            input.onchange = (e) => {
                              const file = (e.target as HTMLInputElement).files?.[0]
                              if (file) handleFileUpload(file)
                            }
                            input.click()
                          }}
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Choose File
                        </Button>
                        <span className="text-sm text-muted-foreground">
                          Supports PDF, DOC, DOCX
                        </span>
                      </div>
                    </div>

                    {/* Job Description Input */}
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Job Description (Optional)</CardTitle>
                        <p className="text-sm text-muted-foreground">
                          Paste the job description to get targeted optimization suggestions
                        </p>
                      </CardHeader>
                      <CardContent>
                        <textarea
                          value={selectedJobDescription}
                          onChange={(e) => setSelectedJobDescription(e.target.value)}
                          placeholder="Paste the job description here for more accurate analysis..."
                          className="w-full h-32 p-3 border border-input bg-background text-foreground rounded-md resize-none focus:outline-none focus:ring-2 focus:ring-ring"
                        />
                        <div className="flex items-center justify-between mt-4">
                          <span className="text-sm text-muted-foreground">
                            {selectedJobDescription.length} characters
                          </span>
                          <Button variant="outline" size="sm">
                            <Brain className="mr-2 h-4 w-4" />
                            Analyze with AI
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Keywords Tab */}
          <TabsContent value="keywords" className="space-y-6">
            {currentAnalysis && (
              <>
                {/* Keywords Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        <span>Found Keywords</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {currentAnalysis.keywords.found.map((keyword, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-green-50 dark:bg-green-950/20 rounded border border-green-200 dark:border-green-800">
                            <span className="text-sm font-medium text-foreground">{keyword}</span>
                            <Badge className="bg-green-100 dark:bg-green-950/20 text-green-800 dark:text-green-400">
                              {currentAnalysis.keywords.density[keyword]?.toFixed(1) || '0.0'}%
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <XCircle className="h-5 w-5 text-red-600" />
                        <span>Missing Keywords</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {currentAnalysis.keywords.missing.map((keyword, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-950/20 rounded border border-red-200 dark:border-red-800">
                            <span className="text-sm font-medium text-foreground">{keyword}</span>
                            <Button size="sm" variant="outline" className="text-xs">
                              <Plus className="h-3 w-3 mr-1" />
                              Add
                            </Button>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <TrendingUp className="h-5 w-5 text-blue-600" />
                        <span>Keyword Density</span>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-3">
                        {Object.entries(currentAnalysis.keywords.density).map(([keyword, density]) => (
                          <div key={keyword} className="space-y-1">
                            <div className="flex items-center justify-between">
                              <span className="text-sm font-medium text-foreground">{keyword}</span>
                              <span className="text-sm text-muted-foreground">{density.toFixed(1)}%</span>
                            </div>
                            <Progress value={density * 10} className="h-2" />
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Keyword Suggestions */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Brain className="h-5 w-5 text-primary" />
                      <span>AI Keyword Suggestions</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <h4 className="font-medium text-foreground">High Priority Keywords</h4>
                        {['TypeScript', 'Node.js', 'AWS'].map((keyword, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg bg-card">
                            <div>
                              <span className="font-medium text-foreground">{keyword}</span>
                              <p className="text-sm text-muted-foreground">Appears in 89% of similar job postings</p>
                            </div>
                            <Button size="sm">
                              <Plus className="h-3 w-3 mr-1" />
                              Add
                            </Button>
                          </div>
                        ))}
                      </div>

                      <div className="space-y-3">
                        <h4 className="font-medium text-foreground">Medium Priority Keywords</h4>
                        {['Agile', 'Scrum', 'Docker'].map((keyword, index) => (
                          <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg bg-card">
                            <div>
                              <span className="font-medium text-foreground">{keyword}</span>
                              <p className="text-sm text-muted-foreground">Appears in 65% of similar job postings</p>
                            </div>
                            <Button size="sm" variant="outline">
                              <Plus className="h-3 w-3 mr-1" />
                              Add
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>

          {/* Suggestions Tab */}
          <TabsContent value="suggestions" className="space-y-6">
            {currentAnalysis && (
              <>
                {/* Suggestions Overview */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <XCircle className="h-8 w-8 text-red-600 dark:text-red-400" />
                        <div>
                          <p className="text-2xl font-bold text-foreground">
                            {currentAnalysis.suggestions.filter(s => s.type === 'critical').length}
                          </p>
                          <p className="text-sm text-muted-foreground">Critical Issues</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <AlertTriangle className="h-8 w-8 text-yellow-600 dark:text-yellow-400" />
                        <div>
                          <p className="text-2xl font-bold text-foreground">
                            {currentAnalysis.suggestions.filter(s => s.type === 'important').length}
                          </p>
                          <p className="text-sm text-muted-foreground">Important</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-2">
                        <Lightbulb className="h-8 w-8 text-blue-600 dark:text-blue-400" />
                        <div>
                          <p className="text-2xl font-bold text-foreground">
                            {currentAnalysis.suggestions.filter(s => s.type === 'minor').length}
                          </p>
                          <p className="text-sm text-muted-foreground">Minor Issues</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Detailed Suggestions */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Lightbulb className="h-5 w-5 text-primary" />
                      <span>Optimization Suggestions</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {currentAnalysis.suggestions.map((suggestion) => (
                        <div key={suggestion.id} className="border border-border rounded-lg p-4 bg-card">
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex items-start space-x-3">
                              {getSuggestionIcon(suggestion.type)}
                              <div>
                                <h4 className="font-medium text-foreground">{suggestion.title}</h4>
                                <p className="text-sm text-muted-foreground mt-1">{suggestion.description}</p>
                              </div>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Badge className={getSuggestionBadgeColor(suggestion.type)}>
                                {suggestion.type}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {suggestion.impact} impact
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {suggestion.effort} fix
                              </Badge>
                            </div>
                          </div>

                          {suggestion.before && suggestion.after && (
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                              <div className="p-3 bg-red-50 dark:bg-red-950/20 rounded border border-red-200 dark:border-red-800">
                                <h5 className="text-sm font-medium text-red-800 dark:text-red-400 mb-2">Before</h5>
                                <p className="text-sm text-foreground">{suggestion.before}</p>
                              </div>
                              <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded border border-green-200 dark:border-green-800">
                                <h5 className="text-sm font-medium text-green-800 dark:text-green-400 mb-2">After</h5>
                                <p className="text-sm text-foreground">{suggestion.after}</p>
                              </div>
                            </div>
                          )}

                          <div className="flex items-center justify-between mt-4">
                            <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                              <span>Category: {suggestion.category}</span>
                              <span>•</span>
                              <span>Impact: {suggestion.impact}</span>
                              <span>•</span>
                              <span>Effort: {suggestion.effort}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button size="sm" variant="outline">
                                <Eye className="h-3 w-3 mr-1" />
                                Preview
                              </Button>
                              <Button size="sm">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Apply Fix
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>

          {/* Templates Tab */}
          <TabsContent value="templates" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5 text-primary" />
                  <span>ATS-Optimized Templates</span>
                </CardTitle>
                <p className="text-sm text-muted-foreground">
                  Choose from professionally designed templates optimized for ATS systems
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {[
                    {
                      id: 'tech',
                      name: 'Technology Professional',
                      description: 'Perfect for software engineers, developers, and IT professionals',
                      atsScore: 95,
                      features: ['Clean formatting', 'Technical skills section', 'Project highlights'],
                      preview: '/templates/tech-preview.png'
                    },
                    {
                      id: 'business',
                      name: 'Business Executive',
                      description: 'Ideal for managers, executives, and business professionals',
                      atsScore: 92,
                      features: ['Leadership focus', 'Achievement metrics', 'Professional summary'],
                      preview: '/templates/business-preview.png'
                    },
                    {
                      id: 'creative',
                      name: 'Creative Professional',
                      description: 'Designed for designers, marketers, and creative roles',
                      atsScore: 88,
                      features: ['Portfolio section', 'Creative projects', 'Visual hierarchy'],
                      preview: '/templates/creative-preview.png'
                    },
                    {
                      id: 'healthcare',
                      name: 'Healthcare Professional',
                      description: 'Tailored for doctors, nurses, and healthcare workers',
                      atsScore: 94,
                      features: ['Certifications focus', 'Clinical experience', 'Education emphasis'],
                      preview: '/templates/healthcare-preview.png'
                    },
                    {
                      id: 'finance',
                      name: 'Finance Professional',
                      description: 'Optimized for accountants, analysts, and finance roles',
                      atsScore: 93,
                      features: ['Quantified achievements', 'Financial metrics', 'Compliance focus'],
                      preview: '/templates/finance-preview.png'
                    },
                    {
                      id: 'education',
                      name: 'Education Professional',
                      description: 'Perfect for teachers, professors, and education administrators',
                      atsScore: 90,
                      features: ['Teaching experience', 'Research highlights', 'Academic achievements'],
                      preview: '/templates/education-preview.png'
                    }
                  ].map((template) => (
                    <Card key={template.id} className="hover:shadow-lg transition-shadow border-2 hover:border-primary/20">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{template.name}</CardTitle>
                          <Badge className={`${getScoreBgColor(template.atsScore)} ${getScoreColor(template.atsScore)} border`}>
                            {template.atsScore}% ATS
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        {/* Template Preview */}
                        <div className="aspect-[3/4] bg-muted/50 rounded-lg border border-border flex items-center justify-center">
                          <FileText className="h-12 w-12 text-muted-foreground" />
                        </div>

                        {/* Features */}
                        <div className="space-y-2">
                          <h4 className="text-sm font-medium text-foreground">Key Features:</h4>
                          <ul className="space-y-1">
                            {template.features.map((feature, index) => (
                              <li key={index} className="flex items-center space-x-2 text-sm text-muted-foreground">
                                <CheckCircle className="h-3 w-3 text-green-500" />
                                <span>{feature}</span>
                              </li>
                            ))}
                          </ul>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2">
                          <Button size="sm" variant="outline" className="flex-1">
                            <Eye className="mr-2 h-3 w-3" />
                            Preview
                          </Button>
                          <Button size="sm" className="flex-1">
                            <Download className="mr-2 h-3 w-3" />
                            Use Template
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Template Customization */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Settings className="h-5 w-5 text-primary" />
                  <span>Template Customization</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h4 className="font-medium text-foreground">Formatting Options</h4>
                    <div className="space-y-3">
                      {[
                        { label: 'Font Style', options: ['Arial', 'Calibri', 'Times New Roman'] },
                        { label: 'Font Size', options: ['10pt', '11pt', '12pt'] },
                        { label: 'Line Spacing', options: ['Single', '1.15', '1.5'] },
                        { label: 'Margins', options: ['Narrow', 'Normal', 'Wide'] }
                      ].map((option, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm font-medium text-foreground">{option.label}</span>
                          <select className="px-3 py-1 border border-input bg-background text-foreground rounded text-sm focus:outline-none focus:ring-2 focus:ring-ring">
                            {option.options.map((opt, i) => (
                              <option key={i} value={opt}>{opt}</option>
                            ))}
                          </select>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="font-medium text-foreground">Section Options</h4>
                    <div className="space-y-3">
                      {[
                        'Professional Summary',
                        'Core Competencies',
                        'Technical Skills',
                        'Certifications',
                        'Projects',
                        'Publications'
                      ].map((section, index) => (
                        <div key={index} className="flex items-center justify-between">
                          <span className="text-sm text-foreground">{section}</span>
                          <Switch defaultChecked={index < 4} />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-end space-x-3 mt-6 pt-6 border-t border-border">
                  <Button variant="outline">
                    <Eye className="mr-2 h-4 w-4" />
                    Preview Changes
                  </Button>
                  <Button>
                    <Download className="mr-2 h-4 w-4" />
                    Download Template
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Analysis Tab */}
          <TabsContent value="analysis" className="space-y-6">
            {currentAnalysis && (
              <>
                {/* ATS Score Overview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <BarChart3 className="h-5 w-5 text-primary" />
                      <span>ATS Compatibility Score</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                      {/* Overall Score */}
                      <div className="text-center">
                        <div className={`text-4xl font-bold ${getScoreColor(currentAnalysis.atsScore.overall)} mb-2`}>
                          {currentAnalysis.atsScore.overall}%
                        </div>
                        <div className="text-sm text-muted-foreground">Overall Score</div>
                        <Progress value={currentAnalysis.atsScore.overall} className="mt-2" />
                      </div>

                      {/* Individual Scores */}
                      {Object.entries(currentAnalysis.atsScore).filter(([key]) => key !== 'overall').map(([key, value]) => (
                        <div key={key} className="text-center">
                          <div className={`text-2xl font-bold ${getScoreColor(value)} mb-2`}>
                            {value}%
                          </div>
                          <div className="text-sm text-muted-foreground capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </div>
                          <Progress value={value} className="mt-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Score Breakdown */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Score Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {Object.entries(currentAnalysis.atsScore).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between">
                          <span className="text-sm font-medium text-foreground capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className={`px-2 py-1 rounded text-xs font-medium ${getScoreBgColor(value)} ${getScoreColor(value)}`}>
                              {value}%
                            </div>
                            {value >= 80 ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : value >= 60 ? (
                              <AlertTriangle className="h-4 w-4 text-yellow-500" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Improvement Areas</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {currentAnalysis.suggestions.slice(0, 3).map((suggestion) => (
                        <div key={suggestion.id} className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                          {getSuggestionIcon(suggestion.type)}
                          <div className="flex-1">
                            <h4 className="font-medium text-foreground text-sm">{suggestion.title}</h4>
                            <p className="text-xs text-muted-foreground mt-1">{suggestion.description}</p>
                          </div>
                          <Badge className={getSuggestionBadgeColor(suggestion.type)}>
                            {suggestion.type}
                          </Badge>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </div>

                {/* Detailed Analysis */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <Brain className="h-5 w-5 text-primary" />
                      <span>Detailed Analysis Report</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-4">
                        <h4 className="font-medium text-foreground">Strengths</h4>
                        <div className="space-y-2">
                          {[
                            'Clean, professional formatting',
                            'Standard section headers used',
                            'Appropriate font and spacing',
                            'Contact information clearly visible'
                          ].map((strength, index) => (
                            <div key={index} className="flex items-center space-x-2">
                              <CheckCircle className="h-4 w-4 text-green-500" />
                              <span className="text-sm text-foreground">{strength}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      <div className="space-y-4">
                        <h4 className="font-medium text-foreground">Areas for Improvement</h4>
                        <div className="space-y-2">
                          {[
                            'Missing industry-specific keywords',
                            'Limited quantified achievements',
                            'Skills section could be more prominent',
                            'Some formatting inconsistencies'
                          ].map((improvement, index) => (
                            <div key={index} className="flex items-center space-x-2">
                              <AlertTriangle className="h-4 w-4 text-yellow-500" />
                              <span className="text-sm text-foreground">{improvement}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </TabsContent>
            {currentAnalysis && (
              <>
                {/* ATS Score Overview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <BarChart3 className="h-5 w-5 text-primary" />
                      <span>ATS Compatibility Score</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                      {/* Overall Score */}
                      <div className="text-center">
                        <div className={`text-4xl font-bold ${getScoreColor(currentAnalysis.atsScore.overall)} mb-2`}>
                          {currentAnalysis.atsScore.overall}%
                        </div>
                        <div className="text-sm text-muted-foreground">Overall Score</div>
                        <Progress value={currentAnalysis.atsScore.overall} className="mt-2" />
                      </div>

                      {/* Individual Scores */}
                      {Object.entries(currentAnalysis.atsScore).filter(([key]) => key !== 'overall').map(([key, value]) => (
                        <div key={key} className="text-center">
                          <div className={`text-2xl font-bold ${getScoreColor(value)} mb-2`}>
                            {value}%
                          </div>
                          <div className="text-sm text-muted-foreground capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </div>
                          <Progress value={value} className="mt-2" />
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Score Breakdown */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle>Score Breakdown</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {Object.entries(currentAnalysis.atsScore).map(([key, value]) => (
                        <div key={key} className="flex items-center justify-between">
                          <span className="text-sm font-medium text-foreground capitalize">
                            {key.replace(/([A-Z])/g, ' $1').trim()}
                          </span>
                          <div className="flex items-center space-x-2">
                            <div className={`px-2 py-1 rounded text-xs font-medium ${getScoreBgColor(value)} ${getScoreColor(value)}`}>
                              {value}%
                            </div>
                            {value >= 80 ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : value >= 60 ? (
                              <AlertTriangle className="h-4 w-4 text-yellow-500" />
                            ) : (
                              <XCircle className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Improvement Areas</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {currentAnalysis.suggestions.slice(0, 3).map((suggestion) => (
                        <div key={suggestion.id} className="flex items-start space-x-3 p-3 bg-muted/50 rounded-lg">
                          {getSuggestionIcon(suggestion.type)}
                          <div className="flex-1">
                            <h4 className="font-medium text-foreground text-sm">{suggestion.title}</h4>
                            <p className="text-xs text-muted-foreground mt-1">{suggestion.description}</p>
                          </div>
                          <Badge className={getSuggestionBadgeColor(suggestion.type)}>
                            {suggestion.type}
                          </Badge>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </div>
              </>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
