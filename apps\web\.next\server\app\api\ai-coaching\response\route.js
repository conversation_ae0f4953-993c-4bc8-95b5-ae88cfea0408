"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/ai-coaching/response/route";
exports.ids = ["app/api/ai-coaching/response/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai-coaching%2Fresponse%2Froute&page=%2Fapi%2Fai-coaching%2Fresponse%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai-coaching%2Fresponse%2Froute.ts&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai-coaching%2Fresponse%2Froute&page=%2Fapi%2Fai-coaching%2Fresponse%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai-coaching%2Fresponse%2Froute.ts&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_apps_InterviewSpark_apps_web_src_app_api_ai_coaching_response_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/ai-coaching/response/route.ts */ \"(rsc)/./src/app/api/ai-coaching/response/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/ai-coaching/response/route\",\n        pathname: \"/api/ai-coaching/response\",\n        filename: \"route\",\n        bundlePath: \"app/api/ai-coaching/response/route\"\n    },\n    resolvedPagePath: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\ai-coaching\\\\response\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_apps_InterviewSpark_apps_web_src_app_api_ai_coaching_response_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/ai-coaching/response/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai-coaching%2Fresponse%2Froute&page=%2Fapi%2Fai-coaching%2Fresponse%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai-coaching%2Fresponse%2Froute.ts&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/ai-coaching/response/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/ai-coaching/response/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _services_aiCoachingSessionService__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/services/aiCoachingSessionService */ \"(rsc)/./src/services/aiCoachingSessionService.ts\");\n\n\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { sessionId, userResponse, responseTime } = body;\n        // Validate required fields\n        if (!sessionId || !userResponse) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields: sessionId, userResponse\"\n            }, {\n                status: 400\n            });\n        }\n        // Process user response\n        const result = await _services_aiCoachingSessionService__WEBPACK_IMPORTED_MODULE_1__.aiCoachingSessionService.processUserResponse(sessionId, userResponse, responseTime || 0);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            evaluation: result.evaluation,\n            nextQuestion: result.nextQuestion,\n            sessionComplete: result.sessionComplete,\n            progress: _services_aiCoachingSessionService__WEBPACK_IMPORTED_MODULE_1__.aiCoachingSessionService.getSessionProgress(sessionId)\n        });\n    } catch (error) {\n        console.error(\"Error processing user response:\", error);\n        if (error instanceof Error && error.message.includes(\"Session\")) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: error.message\n            }, {\n                status: 404\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to process user response\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const sessionId = searchParams.get(\"sessionId\");\n        if (!sessionId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing sessionId parameter\"\n            }, {\n                status: 400\n            });\n        }\n        // Get session progress\n        const progress = _services_aiCoachingSessionService__WEBPACK_IMPORTED_MODULE_1__.aiCoachingSessionService.getSessionProgress(sessionId);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            progress\n        });\n    } catch (error) {\n        console.error(\"Error fetching session progress:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch session progress\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/ai-coaching/response/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/aiCoachingSessionService.ts":
/*!**************************************************!*\
  !*** ./src/services/aiCoachingSessionService.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   aiCoachingSessionService: () => (/* binding */ aiCoachingSessionService)\n/* harmony export */ });\n/* harmony import */ var _llmIntegrationService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./llmIntegrationService */ \"(rsc)/./src/services/llmIntegrationService.ts\");\n\n// Global session storage to persist across API calls\nconst globalActiveSessions = new Map();\nconst globalSessionStorage = new Map();\nclass AICoachingSessionService {\n    // Create a new AI coaching session\n    async createSession(params) {\n        const sessionId = `ai-session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n        const session = {\n            id: sessionId,\n            userId: params.userId,\n            role: params.role,\n            sessionType: params.sessionType,\n            status: \"active\",\n            startTime: new Date(),\n            duration: 0,\n            config: {\n                difficulty: params.config.difficulty || this.getDefaultDifficulty(params.userProfile.level),\n                focusAreas: params.config.focusAreas,\n                timeLimit: params.config.timeLimit,\n                questionCount: params.config.questionCount || 10,\n                adaptiveDifficulty: params.config.adaptiveDifficulty ?? true,\n                emotionalAnalysis: params.config.emotionalAnalysis ?? true\n            },\n            messages: [],\n            questionHistory: [],\n            userProfile: {\n                ...params.userProfile,\n                previousSessions: this.getUserSessionHistory(params.userId).map((s)=>s.id)\n            },\n            performance: {\n                questionsAnswered: 0,\n                averageScore: 0,\n                scores: [],\n                timePerQuestion: [],\n                difficultyProgression: [],\n                strengths: [],\n                improvements: []\n            },\n            emotionalData: {\n                states: [],\n                interventions: [],\n                overallTrends: {\n                    confidenceChange: 0,\n                    stressChange: 0,\n                    engagementChange: 0\n                }\n            },\n            analytics: {\n                totalTokensUsed: 0,\n                llmCalls: 0,\n                averageResponseTime: 0,\n                adaptations: 0,\n                hintsUsed: 0,\n                followUpQuestions: 0\n            }\n        };\n        // Store session\n        this.activeSessions.set(sessionId, session);\n        // Generate first question\n        await this.generateNextQuestion(sessionId);\n        return session;\n    }\n    // Generate the next question in the session\n    async generateNextQuestion(sessionId, userResponse) {\n        const session = this.getSession(sessionId);\n        if (!session) {\n            throw new Error(`Session not found: ${sessionId}`);\n        }\n        const startTime = Date.now();\n        try {\n            // Build coaching context\n            const context = {\n                role: session.role,\n                userLevel: session.userProfile.level,\n                sessionType: session.sessionType,\n                focusAreas: session.config.focusAreas,\n                previousResponses: session.messages,\n                userProfile: {\n                    experience: session.userProfile.experience,\n                    weaknesses: session.userProfile.weaknesses,\n                    goals: session.userProfile.goals\n                },\n                emotionalState: this.getCurrentEmotionalState(session)\n            };\n            // Generate AI response\n            const aiResponse = await _llmIntegrationService__WEBPACK_IMPORTED_MODULE_0__.llmIntegrationService.generateCoachingResponse(context, userResponse);\n            // Adapt difficulty if enabled\n            if (session.config.adaptiveDifficulty && session.performance.scores.length > 0) {\n                const recentPerformance = this.getRecentPerformance(session);\n                const emotionalState = this.getCurrentEmotionalState(session);\n                aiResponse.difficulty = _llmIntegrationService__WEBPACK_IMPORTED_MODULE_0__.llmIntegrationService.adaptDifficulty(aiResponse.difficulty, recentPerformance, emotionalState);\n                session.analytics.adaptations++;\n            }\n            // Store question and update session\n            session.currentQuestion = aiResponse;\n            session.questionHistory.push(aiResponse);\n            session.performance.difficultyProgression.push(aiResponse.difficulty);\n            // Add AI message to conversation\n            session.messages.push({\n                role: \"assistant\",\n                content: aiResponse.message,\n                timestamp: Date.now(),\n                metadata: {\n                    questionType: aiResponse.questionType,\n                    difficulty: aiResponse.difficulty,\n                    category: aiResponse.category\n                }\n            });\n            // Update analytics\n            session.analytics.llmCalls++;\n            session.analytics.averageResponseTime = this.updateAverageResponseTime(session.analytics.averageResponseTime, session.analytics.llmCalls, Date.now() - startTime);\n            return aiResponse;\n        } catch (error) {\n            console.error(\"Error generating next question:\", error);\n            throw error;\n        }\n    }\n    // Process user response and evaluate it\n    async processUserResponse(sessionId, userResponse, responseTime) {\n        const session = this.getSession(sessionId);\n        if (!session || !session.currentQuestion) {\n            throw new Error(`Session or current question not found: ${sessionId}`);\n        }\n        // Add user message to conversation\n        session.messages.push({\n            role: \"user\",\n            content: userResponse,\n            timestamp: Date.now()\n        });\n        // Evaluate the response\n        const evaluation = await _llmIntegrationService__WEBPACK_IMPORTED_MODULE_0__.llmIntegrationService.evaluateResponse(userResponse, session.currentQuestion.evaluation?.expectedPoints || [], {\n            role: session.role,\n            userLevel: session.userProfile.level,\n            sessionType: session.sessionType,\n            focusAreas: session.config.focusAreas,\n            previousResponses: session.messages,\n            userProfile: {\n                experience: session.userProfile.experience,\n                weaknesses: session.userProfile.weaknesses,\n                goals: session.userProfile.goals\n            }\n        });\n        // Update performance metrics\n        session.performance.questionsAnswered++;\n        session.performance.scores.push(evaluation.score);\n        session.performance.timePerQuestion.push(responseTime);\n        session.performance.averageScore = this.calculateAverageScore(session.performance.scores);\n        session.performance.strengths.push(...evaluation.strengths);\n        session.performance.improvements.push(...evaluation.improvements);\n        // Check if session should continue\n        const sessionComplete = this.shouldCompleteSession(session);\n        let nextQuestion;\n        if (!sessionComplete) {\n            // Generate next question\n            nextQuestion = await this.generateNextQuestion(sessionId);\n        } else {\n            // Complete the session\n            await this.completeSession(sessionId);\n        }\n        return {\n            evaluation,\n            nextQuestion,\n            sessionComplete\n        };\n    }\n    // Update emotional state\n    updateEmotionalState(sessionId, emotionalState) {\n        const session = this.getSession(sessionId);\n        if (!session) return;\n        session.emotionalData.states.push({\n            timestamp: Date.now(),\n            ...emotionalState\n        });\n        // Update trends\n        if (session.emotionalData.states.length > 1) {\n            const previous = session.emotionalData.states[session.emotionalData.states.length - 2];\n            const current = session.emotionalData.states[session.emotionalData.states.length - 1];\n            session.emotionalData.overallTrends.confidenceChange = current.confidence - previous.confidence;\n            session.emotionalData.overallTrends.stressChange = current.stress - previous.stress;\n            session.emotionalData.overallTrends.engagementChange = current.engagement - previous.engagement;\n        }\n        // Check for interventions needed\n        this.checkEmotionalInterventions(session, emotionalState);\n    }\n    // Get session progress\n    getSessionProgress(sessionId) {\n        const session = this.getSession(sessionId);\n        if (!session) {\n            throw new Error(`Session not found: ${sessionId}`);\n        }\n        const timeElapsed = Math.floor((Date.now() - session.startTime.getTime()) / 1000);\n        const totalQuestions = session.config.questionCount || 10;\n        const currentQuestionIndex = session.performance.questionsAnswered;\n        return {\n            currentQuestionIndex,\n            totalQuestions,\n            timeElapsed,\n            timeRemaining: session.config.timeLimit ? session.config.timeLimit - timeElapsed : undefined,\n            completionPercentage: Math.round(currentQuestionIndex / totalQuestions * 100),\n            currentDifficulty: session.currentQuestion?.difficulty || session.config.difficulty,\n            averageScore: session.performance.averageScore,\n            recentPerformance: session.performance.scores.slice(-3)\n        };\n    }\n    // Complete session and generate summary\n    async completeSession(sessionId) {\n        const session = this.getSession(sessionId);\n        if (!session) return;\n        session.status = \"completed\";\n        session.endTime = new Date();\n        session.duration = Math.floor((session.endTime.getTime() - session.startTime.getTime()) / 1000);\n        // Generate session summary\n        session.summary = {\n            overallScore: session.performance.averageScore,\n            keyStrengths: [\n                ...new Set(session.performance.strengths)\n            ].slice(0, 5),\n            areasForImprovement: [\n                ...new Set(session.performance.improvements)\n            ].slice(0, 5),\n            recommendations: this.generateRecommendations(session),\n            nextSteps: this.generateNextSteps(session),\n            estimatedLevel: this.estimateUserLevel(session)\n        };\n        // Store in user history\n        this.storeSessionInHistory(session);\n        // Remove from active sessions\n        this.activeSessions.delete(sessionId);\n    }\n    // Helper methods\n    getSession(sessionId) {\n        return this.activeSessions.get(sessionId);\n    }\n    getDefaultDifficulty(level) {\n        const difficultyMap = {\n            \"novice\": 3,\n            \"intermediate\": 5,\n            \"advanced\": 7,\n            \"expert\": 9\n        };\n        return difficultyMap[level] || 5;\n    }\n    getCurrentEmotionalState(session) {\n        const states = session.emotionalData.states;\n        return states.length > 0 ? states[states.length - 1] : undefined;\n    }\n    getRecentPerformance(session) {\n        const recentScores = session.performance.scores.slice(-3);\n        return recentScores.length > 0 ? recentScores.reduce((sum, score)=>sum + score, 0) / recentScores.length : 50;\n    }\n    shouldCompleteSession(session) {\n        // Check question count\n        if (session.config.questionCount && session.performance.questionsAnswered >= session.config.questionCount) {\n            return true;\n        }\n        // Check time limit\n        if (session.config.timeLimit) {\n            const elapsed = Math.floor((Date.now() - session.startTime.getTime()) / 1000);\n            if (elapsed >= session.config.timeLimit) {\n                return true;\n            }\n        }\n        return false;\n    }\n    calculateAverageScore(scores) {\n        return scores.length > 0 ? Math.round(scores.reduce((sum, score)=>sum + score, 0) / scores.length) : 0;\n    }\n    updateAverageResponseTime(currentAvg, count, newTime) {\n        return Math.round((currentAvg * (count - 1) + newTime) / count);\n    }\n    checkEmotionalInterventions(session, state) {\n        // High stress intervention\n        if (state.stress > 0.7) {\n            session.emotionalData.interventions.push({\n                timestamp: Date.now(),\n                trigger: \"high_stress\",\n                action: \"breathing_exercise_suggestion\"\n            });\n        }\n        // Low confidence intervention\n        if (state.confidence < 0.3) {\n            session.emotionalData.interventions.push({\n                timestamp: Date.now(),\n                trigger: \"low_confidence\",\n                action: \"encouragement_message\"\n            });\n        }\n    }\n    generateRecommendations(session) {\n        const recommendations = [];\n        if (session.performance.averageScore < 60) {\n            recommendations.push(\"Focus on fundamental concepts before advancing to complex topics\");\n        }\n        if (session.performance.averageScore > 80) {\n            recommendations.push(\"Consider practicing more advanced scenarios to challenge yourself\");\n        }\n        return recommendations;\n    }\n    generateNextSteps(session) {\n        return [\n            \"Review the areas for improvement identified in this session\",\n            \"Practice similar questions to reinforce learning\",\n            \"Schedule another coaching session to track progress\"\n        ];\n    }\n    estimateUserLevel(session) {\n        const avgScore = session.performance.averageScore;\n        if (avgScore >= 90) return \"expert\";\n        if (avgScore >= 75) return \"advanced\";\n        if (avgScore >= 60) return \"intermediate\";\n        return \"novice\";\n    }\n    getUserSessionHistory(userId) {\n        return this.sessionStorage.get(userId) || [];\n    }\n    storeSessionInHistory(session) {\n        const userSessions = this.getUserSessionHistory(session.userId);\n        userSessions.push(session);\n        this.sessionStorage.set(session.userId, userSessions);\n    }\n    // Public methods for session management\n    pauseSession(sessionId) {\n        const session = this.getSession(sessionId);\n        if (session) {\n            session.status = \"paused\";\n        }\n    }\n    resumeSession(sessionId) {\n        const session = this.getSession(sessionId);\n        if (session) {\n            session.status = \"active\";\n        }\n    }\n    cancelSession(sessionId) {\n        const session = this.getSession(sessionId);\n        if (session) {\n            session.status = \"cancelled\";\n            session.endTime = new Date();\n            this.activeSessions.delete(sessionId);\n        }\n    }\n    getActiveSession(userId) {\n        for (const session of this.activeSessions.values()){\n            if (session.userId === userId && session.status === \"active\") {\n                return session;\n            }\n        }\n        return undefined;\n    }\n    getUserSessions(userId) {\n        return this.getUserSessionHistory(userId);\n    }\n    constructor(){\n        this.activeSessions = globalActiveSessions;\n        this.sessionStorage = globalSessionStorage;\n    }\n}\nconst aiCoachingSessionService = new AICoachingSessionService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/aiCoachingSessionService.ts\n");

/***/ }),

/***/ "(rsc)/./src/services/llmIntegrationService.ts":
/*!***********************************************!*\
  !*** ./src/services/llmIntegrationService.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   llmIntegrationService: () => (/* binding */ llmIntegrationService)\n/* harmony export */ });\n/* harmony import */ var openai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! openai */ \"(rsc)/./node_modules/openai/index.mjs\");\n\nclass LLMIntegrationService {\n    constructor(){\n        this.openaiClient = null;\n        this.geminiClient = null;\n        this.rolePrompts = new Map();\n        this.initializeClients();\n        this.setupRolePrompts();\n        this.defaultProvider = {\n            name: \"openai\",\n            model: \"gpt-4-turbo-preview\",\n            apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY\n        };\n    }\n    initializeClients() {\n        // Initialize OpenAI\n        if (process.env.NEXT_PUBLIC_OPENAI_API_KEY) {\n            this.openaiClient = new openai__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n                apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY,\n                dangerouslyAllowBrowser: true\n            });\n        }\n        // Initialize Gemini (Google AI)\n        if (process.env.NEXT_PUBLIC_GEMINI_API_KEY) {\n            // Gemini client initialization would go here\n            console.log(\"Gemini API key found, client ready for initialization\");\n        }\n    }\n    setupRolePrompts() {\n        this.rolePrompts.set(\"software-engineer\", `\nYou are an expert Software Engineering interview coach with 15+ years of experience at top tech companies (Google, Meta, Amazon, Microsoft). Your expertise includes:\n\n- System Design and Architecture\n- Data Structures and Algorithms\n- Code Quality and Best Practices\n- Technical Leadership and Communication\n- Performance Optimization and Scalability\n\nYour coaching style:\n- Ask progressively challenging questions based on the candidate's responses\n- Provide constructive feedback with specific improvement suggestions\n- Adapt difficulty based on the candidate's performance and emotional state\n- Focus on both technical depth and communication clarity\n- Encourage best practices and industry standards\n\nAlways structure your responses as a professional coach would, being supportive yet challenging.\n`);\n        this.rolePrompts.set(\"product-manager\", `\nYou are a seasoned Product Management interview coach with extensive experience at leading product companies. Your expertise includes:\n\n- Product Strategy and Vision\n- Stakeholder Management and Communication\n- Data-Driven Decision Making\n- Go-to-Market Strategy\n- User Experience and Design Thinking\n\nYour coaching approach:\n- Present realistic product scenarios and case studies\n- Evaluate strategic thinking and prioritization skills\n- Assess communication and leadership capabilities\n- Provide frameworks for structured problem-solving\n- Focus on business impact and user value\n\nGuide candidates through complex product decisions while teaching them to think like senior product leaders.\n`);\n        this.rolePrompts.set(\"data-scientist\", `\nYou are an expert Data Science interview coach with deep experience in machine learning and analytics. Your expertise includes:\n\n- Machine Learning Algorithms and Implementation\n- Statistical Analysis and Hypothesis Testing\n- Data Engineering and Pipeline Design\n- Business Intelligence and Insights\n- Model Deployment and MLOps\n\nYour coaching methodology:\n- Present real-world data problems and case studies\n- Evaluate technical depth and practical application\n- Assess ability to communicate complex concepts simply\n- Focus on end-to-end ML lifecycle understanding\n- Emphasize business impact and ethical considerations\n\nHelp candidates demonstrate both technical expertise and business acumen.\n`);\n    }\n    async generateCoachingResponse(context, userMessage) {\n        try {\n            const systemPrompt = this.buildSystemPrompt(context);\n            const conversationHistory = this.buildConversationHistory(context, userMessage);\n            const llmResponse = await this.callLLM(systemPrompt, conversationHistory, context);\n            return this.parseCoachingResponse(llmResponse, context);\n        } catch (error) {\n            console.error(\"Error generating coaching response:\", error);\n            return this.getFallbackResponse(context);\n        }\n    }\n    buildSystemPrompt(context) {\n        const basePrompt = this.rolePrompts.get(context.role) || this.rolePrompts.get(\"software-engineer\");\n        const contextualPrompt = `\n${basePrompt}\n\nCURRENT SESSION CONTEXT:\n- Role: ${context.role}\n- User Level: ${context.userLevel}\n- Session Type: ${context.sessionType}\n- Focus Areas: ${context.focusAreas.join(\", \")}\n- User Experience: ${context.userProfile.experience.join(\", \")}\n- Areas for Improvement: ${context.userProfile.weaknesses.join(\", \")}\n- Goals: ${context.userProfile.goals.join(\", \")}\n\n${context.emotionalState ? `\nEMOTIONAL STATE AWARENESS:\n- Confidence: ${Math.round(context.emotionalState.confidence * 100)}%\n- Stress Level: ${Math.round(context.emotionalState.stress * 100)}%\n- Engagement: ${Math.round(context.emotionalState.engagement * 100)}%\n\nAdjust your coaching style based on these emotional indicators. If stress is high, be more supportive. If confidence is low, provide encouragement. If engagement is low, make questions more interactive.\n` : \"\"}\n\nRESPONSE FORMAT:\nRespond with a JSON object containing:\n{\n  \"message\": \"Your coaching message or question\",\n  \"questionType\": \"technical|behavioral|situational|follow-up\",\n  \"difficulty\": 1-10,\n  \"category\": \"specific category\",\n  \"hints\": [\"optional hints if user struggles\"],\n  \"followUpQuestions\": [\"potential follow-up questions\"],\n  \"evaluation\": {\n    \"expectedPoints\": [\"key points to look for in response\"],\n    \"scoringCriteria\": [\"how to evaluate the response\"]\n  },\n  \"adaptiveAdjustments\": {\n    \"increaseComplexity\": boolean,\n    \"provideSupportiveGuidance\": boolean,\n    \"focusOnWeakAreas\": boolean\n  }\n}\n`;\n        return contextualPrompt;\n    }\n    buildConversationHistory(context, userMessage) {\n        const messages = [\n            ...context.previousResponses\n        ];\n        if (userMessage) {\n            messages.push({\n                role: \"user\",\n                content: userMessage,\n                timestamp: Date.now()\n            });\n        }\n        return messages;\n    }\n    async callLLM(systemPrompt, conversationHistory, context) {\n        if (!this.openaiClient) {\n            throw new Error(\"No LLM client available\");\n        }\n        const messages = [\n            {\n                role: \"system\",\n                content: systemPrompt\n            },\n            ...conversationHistory.map((msg)=>({\n                    role: msg.role,\n                    content: msg.content\n                }))\n        ];\n        const response = await this.openaiClient.chat.completions.create({\n            model: this.defaultProvider.model,\n            messages,\n            temperature: 0.7,\n            max_tokens: 1000,\n            response_format: {\n                type: \"json_object\"\n            }\n        });\n        return {\n            content: response.choices[0]?.message?.content || \"\",\n            usage: {\n                promptTokens: response.usage?.prompt_tokens || 0,\n                completionTokens: response.usage?.completion_tokens || 0,\n                totalTokens: response.usage?.total_tokens || 0\n            },\n            model: response.model,\n            finishReason: response.choices[0]?.finish_reason || \"stop\"\n        };\n    }\n    parseCoachingResponse(llmResponse, context) {\n        try {\n            const parsed = JSON.parse(llmResponse.content);\n            // Validate and ensure all required fields are present\n            return {\n                message: parsed.message || \"Let's continue with your interview preparation.\",\n                questionType: parsed.questionType || \"technical\",\n                difficulty: Math.max(1, Math.min(10, parsed.difficulty || 5)),\n                category: parsed.category || context.focusAreas[0] || \"General\",\n                hints: Array.isArray(parsed.hints) ? parsed.hints : [],\n                followUpQuestions: Array.isArray(parsed.followUpQuestions) ? parsed.followUpQuestions : [],\n                evaluation: parsed.evaluation || {\n                    expectedPoints: [],\n                    scoringCriteria: []\n                },\n                adaptiveAdjustments: parsed.adaptiveAdjustments || {\n                    increaseComplexity: false,\n                    provideSupportiveGuidance: false,\n                    focusOnWeakAreas: false\n                }\n            };\n        } catch (error) {\n            console.error(\"Error parsing LLM response:\", error);\n            return this.getFallbackResponse(context);\n        }\n    }\n    getFallbackResponse(context) {\n        const fallbackQuestions = {\n            \"software-engineer\": \"Can you walk me through how you would design a URL shortening service like bit.ly?\",\n            \"product-manager\": \"How would you prioritize features for a new mobile app with limited engineering resources?\",\n            \"data-scientist\": \"Describe how you would approach building a recommendation system for an e-commerce platform.\"\n        };\n        return {\n            message: fallbackQuestions[context.role] || \"Tell me about a challenging project you worked on and how you approached it.\",\n            questionType: \"technical\",\n            difficulty: 5,\n            category: \"General Problem Solving\",\n            hints: [\n                \"Think about the problem systematically\",\n                \"Consider scalability and trade-offs\"\n            ],\n            followUpQuestions: [\n                \"What challenges did you face?\",\n                \"How would you improve your solution?\"\n            ],\n            evaluation: {\n                expectedPoints: [\n                    \"Problem understanding\",\n                    \"Systematic approach\",\n                    \"Technical depth\"\n                ],\n                scoringCriteria: [\n                    \"Clarity of explanation\",\n                    \"Technical accuracy\",\n                    \"Consideration of trade-offs\"\n                ]\n            },\n            adaptiveAdjustments: {\n                increaseComplexity: false,\n                provideSupportiveGuidance: true,\n                focusOnWeakAreas: false\n            }\n        };\n    }\n    // Method to evaluate user responses\n    async evaluateResponse(userResponse, expectedPoints, context) {\n        try {\n            const evaluationPrompt = `\nEvaluate this interview response based on the expected points and provide detailed feedback.\n\nRESPONSE TO EVALUATE: \"${userResponse}\"\n\nEXPECTED POINTS: ${expectedPoints.join(\", \")}\n\nCONTEXT: ${context.role} interview, ${context.userLevel} level\n\nProvide evaluation as JSON:\n{\n  \"score\": 1-100,\n  \"feedback\": [\"specific feedback points\"],\n  \"strengths\": [\"what they did well\"],\n  \"improvements\": [\"areas to improve\"],\n  \"nextSteps\": [\"actionable next steps\"]\n}\n`;\n            const response = await this.callLLM(evaluationPrompt, [], context);\n            const evaluation = JSON.parse(response.content);\n            return {\n                score: Math.max(0, Math.min(100, evaluation.score || 50)),\n                feedback: evaluation.feedback || [],\n                strengths: evaluation.strengths || [],\n                improvements: evaluation.improvements || [],\n                nextSteps: evaluation.nextSteps || []\n            };\n        } catch (error) {\n            console.error(\"Error evaluating response:\", error);\n            return {\n                score: 50,\n                feedback: [\n                    \"Unable to evaluate response at this time\"\n                ],\n                strengths: [],\n                improvements: [],\n                nextSteps: [\n                    \"Continue practicing and refining your approach\"\n                ]\n            };\n        }\n    }\n    // Method to adapt difficulty based on performance\n    adaptDifficulty(currentDifficulty, userPerformance, emotionalState) {\n        let newDifficulty = currentDifficulty;\n        // Adjust based on performance\n        if (userPerformance > 80) {\n            newDifficulty += 1;\n        } else if (userPerformance < 40) {\n            newDifficulty -= 1;\n        }\n        // Adjust based on emotional state\n        if (emotionalState) {\n            if (emotionalState.stress > 0.7) {\n                newDifficulty -= 1 // Reduce difficulty if stressed\n                ;\n            }\n            if (emotionalState.confidence < 0.3) {\n                newDifficulty -= 0.5 // Slightly reduce difficulty if low confidence\n                ;\n            }\n        }\n        return Math.max(1, Math.min(10, newDifficulty));\n    }\n    // Method to switch LLM providers\n    switchProvider(provider) {\n        this.defaultProvider = provider;\n        // Re-initialize clients if needed\n        this.initializeClients();\n    }\n    // Method to get available providers\n    getAvailableProviders() {\n        const providers = [];\n        if (process.env.NEXT_PUBLIC_OPENAI_API_KEY) {\n            providers.push({\n                name: \"openai\",\n                model: \"gpt-4-turbo-preview\",\n                apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY\n            });\n            providers.push({\n                name: \"openai\",\n                model: \"gpt-3.5-turbo\",\n                apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY\n            });\n        }\n        if (process.env.NEXT_PUBLIC_GEMINI_API_KEY) {\n            providers.push({\n                name: \"gemini\",\n                model: \"gemini-pro\",\n                apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY\n            });\n        }\n        return providers;\n    }\n}\nconst llmIntegrationService = new LLMIntegrationService();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/services/llmIntegrationService.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/openai"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fai-coaching%2Fresponse%2Froute&page=%2Fapi%2Fai-coaching%2Fresponse%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fai-coaching%2Fresponse%2Froute.ts&appDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CInterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();