{"name": "@web/app", "version": "2.0.0", "description": "AI-InterviewSpark Web Application - Next Generation Frontend", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\""}, "dependencies": {"@anthropic-ai/sdk": "^0.57.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-collapsible": "^1.0.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-hover-card": "^1.0.7", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-menubar": "^1.0.4", "@radix-ui/react-navigation-menu": "^1.1.4", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-scroll-area": "^1.0.5", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-toggle": "^1.0.3", "@radix-ui/react-toggle-group": "^1.0.4", "@radix-ui/react-tooltip": "^1.0.7", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.17.0", "@tanstack/react-query-devtools": "^5.83.0", "@tremor/react": "^3.18.7", "axios": "^1.6.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.0", "cmdk": "^0.2.0", "date-fns": "^3.6.0", "embla-carousel-react": "^8.0.0", "framer-motion": "^10.18.0", "js-cookie": "^3.0.5", "lucide-react": "^0.312.0", "next": "^14.1.0", "next-themes": "^0.2.1", "openai": "^5.10.2", "react": "^18.2.0", "react-countdown": "^2.3.5", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.5.3", "react-player": "^2.14.1", "react-use": "^17.4.2", "react-webcam": "^7.2.0", "recharts": "^2.15.4", "socket.io-client": "^4.7.4", "sonner": "^1.4.0", "tailwind-merge": "^2.2.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.76", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.2.0", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.2", "@types/js-cookie": "^3.0.6", "@types/node": "^20.11.0", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "autoprefixer": "^10.4.17", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.33", "prettier": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.4.1", "typescript": "^5.3.3"}}