"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/coaching/ai/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/coaching/ai/page.tsx":
/*!************************************************!*\
  !*** ./src/app/dashboard/coaching/ai/page.tsx ***!
  \************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AICoachingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_progress__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/progress */ \"(app-pages-browser)/./src/components/ui/progress.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/lightbulb.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video-off.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,BarChart3,Brain,Heart,Lightbulb,MessageCircle,Mic,MicOff,Pause,Play,Send,Square,Target,Video,VideoOff!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic-off.js\");\n/* harmony import */ var _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/aiCoachingClient */ \"(app-pages-browser)/./src/services/aiCoachingClient.ts\");\n/* harmony import */ var _services_emotionalIntelligenceService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/emotionalIntelligenceService */ \"(app-pages-browser)/./src/services/emotionalIntelligenceService.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction AICoachingPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const role = searchParams.get(\"role\") || \"software-engineer\";\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentResponse, setCurrentResponse] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showHints, setShowHints] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [questionCount, setQuestionCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [emotionalState, setEmotionalState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        confidence: 75,\n        stress: 30,\n        engagement: 80,\n        clarity: 70\n    });\n    const [sessionConfig, setSessionConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        sessionType: \"practice\",\n        difficulty: 5,\n        focusAreas: [\n            \"System Design\",\n            \"Algorithms\"\n        ],\n        timeLimit: 3600,\n        questionCount: 10,\n        adaptiveDifficulty: true,\n        emotionalAnalysis: true\n    });\n    const responseTextareaRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const mediaStreamRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        initializeSession();\n        return ()=>{\n            // Cleanup on unmount\n            if (mediaStreamRef.current) {\n                mediaStreamRef.current.getTracks().forEach((track)=>track.stop());\n            }\n        };\n    }, [\n        role\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            // Update progress once when session is created\n            updateProgress();\n            // Set up periodic updates only if session is active\n            if (session.status === \"active\") {\n                const interval = setInterval(()=>{\n                    updateProgress();\n                }, 5000) // Update every 5 seconds instead of 1 second\n                ;\n                return ()=>clearInterval(interval);\n            }\n        }\n    }, [\n        session === null || session === void 0 ? void 0 : session.id,\n        session === null || session === void 0 ? void 0 : session.status\n    ]);\n    const initializeSession = async ()=>{\n        try {\n            setIsLoading(true);\n            // Create a simple demo session without API calls for now\n            const demoSession = {\n                id: \"demo-session-\".concat(Date.now()),\n                role,\n                status: \"active\",\n                startTime: new Date(),\n                config: sessionConfig,\n                userProfile: {\n                    level: \"intermediate\",\n                    experience: [\n                        \"JavaScript\",\n                        \"React\",\n                        \"Node.js\"\n                    ],\n                    weaknesses: [\n                        \"System Design\",\n                        \"Algorithms\"\n                    ],\n                    goals: [\n                        \"Pass technical interviews\",\n                        \"Improve problem-solving skills\"\n                    ]\n                }\n            };\n            setSession(demoSession);\n            // Generate first demo question\n            const demoQuestions = {\n                \"software-engineer\": \"Can you walk me through how you would design a URL shortening service like bit.ly? Consider the system architecture, database design, and scalability requirements.\",\n                \"product-manager\": \"How would you prioritize features for a new mobile app with limited engineering resources? Walk me through your decision-making process.\",\n                \"data-scientist\": \"Describe how you would approach building a recommendation system for an e-commerce platform. What data would you need and what algorithms would you consider?\"\n            };\n            setCurrentQuestion(demoQuestions[role] || demoQuestions[\"software-engineer\"]);\n            setQuestionCount(1);\n            // Set initial progress\n            setProgress({\n                currentQuestionIndex: 1,\n                totalQuestions: 10,\n                timeElapsed: 0,\n                completionPercentage: 10,\n                currentDifficulty: sessionConfig.difficulty,\n                averageScore: 0,\n                recentPerformance: []\n            });\n            // Start media capture for emotional analysis\n            if (sessionConfig.emotionalAnalysis) {\n                await startMediaCapture();\n            }\n        } catch (error) {\n            console.error(\"Error initializing session:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const updateProgress = ()=>{\n        if (session && progress) {\n            // Update time elapsed\n            const timeElapsed = Math.floor((Date.now() - session.startTime.getTime()) / 1000);\n            setProgress((prev)=>prev ? {\n                    ...prev,\n                    timeElapsed,\n                    completionPercentage: Math.round(questionCount / 10 * 100)\n                } : null);\n        }\n    };\n    const startMediaCapture = async ()=>{\n        try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n                video: true,\n                audio: true\n            });\n            mediaStreamRef.current = stream;\n            setIsRecording(true);\n            // Start periodic emotional analysis\n            startEmotionalAnalysis();\n        } catch (error) {\n            console.error(\"Error accessing media devices:\", error);\n        }\n    };\n    const startEmotionalAnalysis = ()=>{\n        setInterval(async ()=>{\n            if (!session || !mediaStreamRef.current) return;\n            try {\n                // Capture audio/video snippets for analysis\n                const audioBlob = await captureAudioSnippet();\n                const videoBlob = await captureVideoSnippet();\n                // Process emotional data\n                const analysis = await _services_emotionalIntelligenceService__WEBPACK_IMPORTED_MODULE_9__.emotionalIntelligenceService.processRealTimeEmotionalData(session.id, audioBlob, videoBlob);\n                // Update emotional state\n                const newEmotionalState = {\n                    confidence: Math.round(analysis.overallState.confidence * 100),\n                    stress: Math.round(analysis.overallState.stress * 100),\n                    engagement: Math.round(analysis.overallState.engagement * 100),\n                    clarity: Math.round(analysis.overallState.clarity * 100)\n                };\n                setEmotionalState(newEmotionalState);\n                // Update session with emotional data\n                await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.updateEmotionalState(session.id, {\n                    confidence: analysis.overallState.confidence,\n                    stress: analysis.overallState.stress,\n                    engagement: analysis.overallState.engagement,\n                    clarity: analysis.overallState.clarity\n                });\n            } catch (error) {\n                console.error(\"Error in emotional analysis:\", error);\n            }\n        }, 5000) // Analyze every 5 seconds\n        ;\n    };\n    const submitResponse = async ()=>{\n        if (!session || !currentResponse.trim()) return;\n        try {\n            setIsLoading(true);\n            // Simulate processing time\n            await new Promise((resolve)=>setTimeout(resolve, 1000));\n            // Generate next demo question\n            const nextQuestions = [\n                \"Great! Now let's dive deeper. How would you handle caching in your URL shortening service?\",\n                \"Excellent analysis. What about security considerations? How would you prevent abuse?\",\n                \"Good thinking. Now, how would you monitor and scale this system as it grows?\",\n                \"Perfect! Let's discuss the database schema. What tables would you create?\",\n                \"Wonderful! How would you handle analytics and click tracking?\",\n                \"Great job! What about rate limiting and API design?\",\n                \"Excellent! How would you implement custom short URLs?\",\n                \"Perfect! What about geographic distribution and CDNs?\",\n                \"Outstanding! How would you handle link expiration?\",\n                \"Fantastic work! You've completed the session successfully!\"\n            ];\n            const nextQuestionIndex = questionCount;\n            if (nextQuestionIndex < nextQuestions.length) {\n                setCurrentQuestion(nextQuestions[nextQuestionIndex]);\n                setQuestionCount((prev)=>prev + 1);\n                // Update progress\n                setProgress((prev)=>prev ? {\n                        ...prev,\n                        currentQuestionIndex: questionCount + 1,\n                        completionPercentage: Math.round((questionCount + 1) / 10 * 100),\n                        averageScore: Math.round(75 + Math.random() * 20),\n                        recentPerformance: [\n                            ...prev.recentPerformance,\n                            Math.round(70 + Math.random() * 25)\n                        ]\n                    } : null);\n                // Simulate emotional state changes\n                setEmotionalState((prev)=>({\n                        confidence: Math.max(20, Math.min(95, prev.confidence + (Math.random() - 0.3) * 10)),\n                        stress: Math.max(10, Math.min(80, prev.stress + (Math.random() - 0.6) * 8)),\n                        engagement: Math.max(30, Math.min(95, prev.engagement + (Math.random() - 0.2) * 5)),\n                        clarity: Math.max(40, Math.min(95, prev.clarity + (Math.random() - 0.1) * 8))\n                    }));\n            } else {\n                // Session complete\n                handleSessionComplete();\n            }\n            // Clear response\n            setCurrentResponse(\"\");\n        } catch (error) {\n            console.error(\"Error submitting response:\", error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleSessionComplete = ()=>{\n        // Navigate to results page\n        router.push(\"/dashboard/coaching/results?sessionId=\".concat(session === null || session === void 0 ? void 0 : session.id));\n    };\n    const pauseSession = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.pauseSession(session.id);\n            if (result.success) {\n                setSession((prev)=>prev ? {\n                        ...prev,\n                        status: \"paused\"\n                    } : null);\n            }\n        }\n    };\n    const resumeSession = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            const result = await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.resumeSession(session.id);\n            if (result.success) {\n                setSession((prev)=>prev ? {\n                        ...prev,\n                        status: \"active\"\n                    } : null);\n            }\n        }\n    };\n    const endSession = async ()=>{\n        if (session === null || session === void 0 ? void 0 : session.id) {\n            await _services_aiCoachingClient__WEBPACK_IMPORTED_MODULE_8__.aiCoachingClient.cancelSession(session.id);\n            router.push(\"/dashboard/experts\");\n        }\n    };\n    // Mock functions for media capture\n    const captureAudioSnippet = async ()=>{\n        return new Blob([\n            \"mock audio\"\n        ], {\n            type: \"audio/wav\"\n        });\n    };\n    const captureVideoSnippet = async ()=>{\n        return new Blob([\n            \"mock video\"\n        ], {\n            type: \"video/mp4\"\n        });\n    };\n    const getEmotionalColor = function(value) {\n        let isStress = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;\n        if (isStress) {\n            if (value > 70) return \"text-red-600\";\n            if (value > 40) return \"text-yellow-600\";\n            return \"text-green-600\";\n        } else {\n            if (value > 80) return \"text-green-600\";\n            if (value > 60) return \"text-yellow-600\";\n            return \"text-red-600\";\n        }\n    };\n    const formatTime = (seconds)=>{\n        const mins = Math.floor(seconds / 60);\n        const secs = seconds % 60;\n        return \"\".concat(mins, \":\").concat(secs.toString().padStart(2, \"0\"));\n    };\n    if (isLoading && !session) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                        className: \"h-12 w-12 text-blue-600 animate-pulse mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                        lineNumber: 333,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Initializing AI Coach...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Setting up your personalized coaching session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                lineNumber: 332,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n            lineNumber: 331,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>router.push(\"/dashboard/experts\"),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"Back to Experts\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 347,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-6 w-6 text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                            className: \"text-2xl font-bold text-gray-900\",\n                                            children: [\n                                                \"AI Coach - \",\n                                                role.replace(\"-\", \" \").toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        (session === null || session === void 0 ? void 0 : session.status) === \"active\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                            variant: \"outline\",\n                                            className: \"ml-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Live\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 346,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                (session === null || session === void 0 ? void 0 : session.status) === \"active\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: pauseSession,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Pause\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: resumeSession,\n                                    variant: \"outline\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 377,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Resume\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 376,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                    onClick: endSession,\n                                    variant: \"destructive\",\n                                    size: \"sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"End Session\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                    lineNumber: 345,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"lg:col-span-3 space-y-6\",\n                            children: [\n                                progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Session Progress\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            progress.currentQuestionIndex,\n                                                            \" / \",\n                                                            progress.totalQuestions,\n                                                            \" questions\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 398,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                value: progress.completionPercentage,\n                                                className: \"mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between text-xs text-gray-500\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Time: \",\n                                                            formatTime(progress.timeElapsed)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Avg Score: \",\n                                                            progress.averageScore,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \"Difficulty: \",\n                                                            progress.currentDifficulty,\n                                                            \"/10\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 406,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 403,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                        lineNumber: 395,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 15\n                                }, this),\n                                currentQuestion && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 418,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Current Question\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: \"Technical\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 422,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: \"outline\",\n                                                                children: [\n                                                                    \"Difficulty: \",\n                                                                    sessionConfig.difficulty,\n                                                                    \"/10\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 421,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 416,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-lg leading-relaxed\",\n                                                        children: currentQuestion\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 433,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4 text-gray-500\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm text-gray-600\",\n                                                                children: [\n                                                                    \"Category: \",\n                                                                    sessionConfig.focusAreas[0] || \"System Design\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 439,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 437,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    showHints && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"h-4 w-4 text-yellow-600\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 447,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium text-yellow-800\",\n                                                                        children: \"Hints\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 448,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 446,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                                className: \"text-sm text-yellow-700 space-y-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Think about the core components and their interactions\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 451,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Consider scalability and performance requirements\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                        children: \"• Don't forget about data consistency and reliability\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 450,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 431,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 466,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Your Response\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 467,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                                        ref: responseTextareaRef,\n                                                        value: currentResponse,\n                                                        onChange: (e)=>setCurrentResponse(e.target.value),\n                                                        placeholder: \"Type your response here... Be specific and explain your thinking process.\",\n                                                        rows: 6,\n                                                        className: \"resize-none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        onClick: ()=>setShowHints(!showHints),\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                                lineNumber: 488,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            showHints ? \"Hide Hints\" : \"Show Hints\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 483,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        size: \"sm\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                                className: \"h-4 w-4 mr-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                                lineNumber: 493,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            \"Voice Input\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                        lineNumber: 492,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 482,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                onClick: submitResponse,\n                                                                disabled: !currentResponse.trim() || isLoading,\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                            lineNumber: 505,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Processing...\"\n                                                                    ]\n                                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-2\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        \"Submit Response\"\n                                                                    ]\n                                                                }, void 0, true)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 498,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 527,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Live Metrics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 528,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 526,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 525,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Confidence\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 534,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.confidence)),\n                                                                    children: [\n                                                                        emotionalState.confidence,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 535,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 533,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.confidence,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 539,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 532,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Stress Level\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 544,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.stress, true)),\n                                                                    children: [\n                                                                        emotionalState.stress,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 545,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.stress,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 549,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Engagement\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 554,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.engagement)),\n                                                                    children: [\n                                                                        emotionalState.engagement,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 555,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 553,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.engagement,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 559,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Clarity\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 564,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm font-bold \".concat(getEmotionalColor(emotionalState.clarity)),\n                                                                    children: [\n                                                                        emotionalState.clarity,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                    lineNumber: 565,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 563,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_progress__WEBPACK_IMPORTED_MODULE_7__.Progress, {\n                                                            value: emotionalState.clarity,\n                                                            className: \"h-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 531,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5 text-blue-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 578,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Session Stats\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 579,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 576,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: progress && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Questions Answered\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 586,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: progress.currentQuestionIndex\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 587,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 585,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Average Score\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 590,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    progress.averageScore,\n                                                                    \"%\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 591,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 589,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Time Elapsed\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 594,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: formatTime(progress.timeElapsed)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 595,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 593,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"Current Difficulty\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 598,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: [\n                                                                    progress.currentDifficulty,\n                                                                    \"/10\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 599,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 597,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 582,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 575,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 text-purple-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 610,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Media\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                        lineNumber: 611,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                lineNumber: 609,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 608,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Camera\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 616,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 36\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 618,\n                                                                columnNumber: 68\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 617,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 615,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"Microphone\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 622,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: isRecording ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 36\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_BarChart3_Brain_Heart_Lightbulb_MessageCircle_Mic_MicOff_Pause_Play_Send_Square_Target_Video_VideoOff_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                                lineNumber: 624,\n                                                                columnNumber: 66\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                            lineNumber: 623,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 621,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: isRecording ? \"Recording for emotional analysis\" : \"Media access disabled\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                            lineNumber: 614,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n            lineNumber: 343,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\dashboard\\\\coaching\\\\ai\\\\page.tsx\",\n        lineNumber: 342,\n        columnNumber: 5\n    }, this);\n}\n_s(AICoachingPage, \"/6ARdghxOLQk9Ygn5SpuWlqluZo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = AICoachingPage;\nvar _c;\n$RefreshReg$(_c, \"AICoachingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/coaching/ai/page.tsx\n"));

/***/ })

});