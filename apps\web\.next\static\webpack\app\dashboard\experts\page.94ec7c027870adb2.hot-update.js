"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/experts/page",{

/***/ "(app-pages-browser)/./src/components/coaching/AICoachingConfig.tsx":
/*!******************************************************!*\
  !*** ./src/components/coaching/AICoachingConfig.tsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ AICoachingConfig; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,Heart,Info,Settings,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,Heart,Info,Settings,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,Heart,Info,Settings,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,Heart,Info,Settings,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,Heart,Info,Settings,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,Heart,Info,Settings,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,Heart,Info,Settings,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,BarChart3,Brain,Heart,Info,Settings,Target,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction AICoachingConfig(param) {\n    let { role, onConfigChange, onStartSession, initialConfig } = param;\n    var _llmProviders_find;\n    _s();\n    const [config, setConfig] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        sessionType: \"practice\",\n        difficulty: 5,\n        focusAreas: [],\n        questionCount: 10,\n        adaptiveDifficulty: true,\n        emotionalAnalysis: true,\n        userProfile: {\n            level: \"intermediate\",\n            experience: [],\n            weaknesses: [],\n            goals: []\n        },\n        llmProvider: \"openai\",\n        llmModel: \"gpt-4-turbo-preview\",\n        ...initialConfig\n    });\n    const sessionTypes = [\n        {\n            id: \"practice\",\n            label: \"Practice Session\",\n            description: \"Casual practice with adaptive questions\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            color: \"bg-blue-100 text-blue-800\"\n        },\n        {\n            id: \"mock-interview\",\n            label: \"Mock Interview\",\n            description: \"Realistic interview simulation\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"bg-purple-100 text-purple-800\"\n        },\n        {\n            id: \"skill-assessment\",\n            label: \"Skill Assessment\",\n            description: \"Evaluate your current skill level\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"bg-green-100 text-green-800\"\n        },\n        {\n            id: \"behavioral\",\n            label: \"Behavioral Questions\",\n            description: \"Focus on behavioral and situational questions\",\n            icon: _barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"bg-orange-100 text-orange-800\"\n        }\n    ];\n    const focusAreasByRole = {\n        \"software-engineer\": [\n            \"System Design\",\n            \"Algorithms\",\n            \"Data Structures\",\n            \"Code Quality\",\n            \"Software Architecture\",\n            \"Database Design\",\n            \"API Design\",\n            \"Performance Optimization\",\n            \"Testing\",\n            \"DevOps\",\n            \"Security\",\n            \"Scalability\"\n        ],\n        \"product-manager\": [\n            \"Product Strategy\",\n            \"Stakeholder Management\",\n            \"Data Analysis\",\n            \"Go-to-Market\",\n            \"User Research\",\n            \"Product Roadmap\",\n            \"Metrics & KPIs\",\n            \"Competitive Analysis\",\n            \"Pricing Strategy\",\n            \"Product Launch\",\n            \"Team Leadership\",\n            \"Customer Development\"\n        ],\n        \"data-scientist\": [\n            \"Machine Learning\",\n            \"Statistics\",\n            \"Data Visualization\",\n            \"Python/R\",\n            \"SQL\",\n            \"Deep Learning\",\n            \"Feature Engineering\",\n            \"Model Deployment\",\n            \"A/B Testing\",\n            \"Business Intelligence\",\n            \"Data Pipeline\",\n            \"MLOps\"\n        ],\n        \"ux-designer\": [\n            \"User Research\",\n            \"Design Systems\",\n            \"Prototyping\",\n            \"Usability Testing\",\n            \"Information Architecture\",\n            \"Interaction Design\",\n            \"Visual Design\",\n            \"Accessibility\",\n            \"Design Thinking\",\n            \"User Journey Mapping\",\n            \"Wireframing\",\n            \"Design Tools\"\n        ]\n    };\n    const llmProviders = [\n        {\n            id: \"openai\",\n            label: \"OpenAI GPT\",\n            models: [\n                \"gpt-4-turbo-preview\",\n                \"gpt-3.5-turbo\"\n            ]\n        },\n        {\n            id: \"gemini\",\n            label: \"Google Gemini\",\n            models: [\n                \"gemini-pro\",\n                \"gemini-pro-vision\"\n            ]\n        },\n        {\n            id: \"claude\",\n            label: \"Anthropic Claude\",\n            models: [\n                \"claude-3-opus\",\n                \"claude-3-sonnet\"\n            ]\n        }\n    ];\n    const updateConfig = (updates)=>{\n        const newConfig = {\n            ...config,\n            ...updates\n        };\n        setConfig(newConfig);\n        onConfigChange(newConfig);\n    };\n    const toggleFocusArea = (area)=>{\n        const newFocusAreas = config.focusAreas.includes(area) ? config.focusAreas.filter((a)=>a !== area) : [\n            ...config.focusAreas,\n            area\n        ];\n        updateConfig({\n            focusAreas: newFocusAreas\n        });\n    };\n    const updateUserProfile = (updates)=>{\n        updateConfig({\n            userProfile: {\n                ...config.userProfile,\n                ...updates\n            }\n        });\n    };\n    const addExperience = (experience)=>{\n        if (experience.trim() && !config.userProfile.experience.includes(experience.trim())) {\n            updateUserProfile({\n                experience: [\n                    ...config.userProfile.experience,\n                    experience.trim()\n                ]\n            });\n        }\n    };\n    const removeExperience = (experience)=>{\n        updateUserProfile({\n            experience: config.userProfile.experience.filter((e)=>e !== experience)\n        });\n    };\n    const getDifficultyLabel = (difficulty)=>{\n        if (difficulty <= 3) return \"Beginner\";\n        if (difficulty <= 6) return \"Intermediate\";\n        if (difficulty <= 8) return \"Advanced\";\n        return \"Expert\";\n    };\n    const getDifficultyColor = (difficulty)=>{\n        if (difficulty <= 3) return \"text-green-600\";\n        if (difficulty <= 6) return \"text-yellow-600\";\n        if (difficulty <= 8) return \"text-orange-600\";\n        return \"text-red-600\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-600 mb-2\",\n                        children: \"Configure AI Coaching Session\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 185,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: [\n                            \"Customize your \",\n                            role.replace(\"-\", \" \"),\n                            \" coaching experience\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                lineNumber: 184,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-5 w-5 text-blue-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Session Type\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                            children: sessionTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-4 border-2 rounded-lg cursor-pointer transition-all \".concat(config.sessionType === type.id ? \"border-blue-500 bg-blue-50\" : \"border-gray-200 hover:border-gray-300\"),\n                                    onClick: ()=>updateConfig({\n                                            sessionType: type.id\n                                        }),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(type.icon, {\n                                                className: \"h-6 w-6 text-blue-600 mt-1\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium text-gray-600\",\n                                                        children: type.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mt-1\",\n                                                        children: type.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 215,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                        className: \"mt-2 \".concat(type.color),\n                                                        children: type.label\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 17\n                                    }, this)\n                                }, type.id, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-5 w-5 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Difficulty & Duration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 230,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Difficulty Level\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"range\",\n                                                        min: \"1\",\n                                                        max: \"10\",\n                                                        value: config.difficulty,\n                                                        onChange: (e)=>updateConfig({\n                                                                difficulty: parseInt(e.target.value)\n                                                            }),\n                                                        className: \"w-full\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Beginner\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                                lineNumber: 249,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium \".concat(getDifficultyColor(config.difficulty)),\n                                                                children: [\n                                                                    config.difficulty,\n                                                                    \"/10 - \",\n                                                                    getDifficultyLabel(config.difficulty)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                                lineNumber: 250,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"Expert\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                                lineNumber: 253,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Questions\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        type: \"number\",\n                                                        min: \"5\",\n                                                        max: \"50\",\n                                                        value: config.questionCount,\n                                                        onChange: (e)=>updateConfig({\n                                                                questionCount: parseInt(e.target.value) || 10\n                                                            }),\n                                                        className: \"mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 261,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        className: \"text-sm font-medium\",\n                                                        children: \"Time Limit (min)\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                                        type: \"number\",\n                                                        min: \"15\",\n                                                        max: \"180\",\n                                                        value: config.timeLimit ? config.timeLimit / 60 : \"\",\n                                                        onChange: (e)=>updateConfig({\n                                                                timeLimit: e.target.value ? parseInt(e.target.value) * 60 : undefined\n                                                            }),\n                                                        placeholder: \"No limit\",\n                                                        className: \"mt-1\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 272,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"adaptive\",\n                                                        checked: config.adaptiveDifficulty,\n                                                        onChange: (e)=>updateConfig({\n                                                                adaptiveDifficulty: e.target.checked\n                                                            }),\n                                                        className: \"rounded border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"adaptive\",\n                                                        className: \"text-sm\",\n                                                        children: \"Adaptive difficulty based on performance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 295,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"checkbox\",\n                                                        id: \"emotional\",\n                                                        checked: config.emotionalAnalysis,\n                                                        onChange: (e)=>updateConfig({\n                                                                emotionalAnalysis: e.target.checked\n                                                            }),\n                                                        className: \"rounded border-gray-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                        htmlFor: \"emotional\",\n                                                        className: \"text-sm\",\n                                                        children: \"Real-time emotional intelligence analysis\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 229,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                            className: \"h-5 w-5 text-purple-600\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"AI Model Settings\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"LLM Provider\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: config.llmProvider,\n                                                onChange: (e)=>{\n                                                    var _llmProviders_find;\n                                                    return updateConfig({\n                                                        llmProvider: e.target.value,\n                                                        llmModel: ((_llmProviders_find = llmProviders.find((p)=>p.id === e.target.value)) === null || _llmProviders_find === void 0 ? void 0 : _llmProviders_find.models[0]) || \"\"\n                                                    });\n                                                },\n                                                className: \"w-full mt-1 p-2 border border-gray-300 rounded-md\",\n                                                children: llmProviders.map((provider)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: provider.id,\n                                                        children: provider.label\n                                                    }, provider.id, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 324,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Model\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 343,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: config.llmModel,\n                                                onChange: (e)=>updateConfig({\n                                                        llmModel: e.target.value\n                                                    }),\n                                                className: \"w-full mt-1 p-2 border border-gray-300 rounded-md\",\n                                                children: (_llmProviders_find = llmProviders.find((p)=>p.id === config.llmProvider)) === null || _llmProviders_find === void 0 ? void 0 : _llmProviders_find.models.map((model)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: model,\n                                                        children: model\n                                                    }, model, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"User Level\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 360,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                value: config.userProfile.level,\n                                                onChange: (e)=>updateUserProfile({\n                                                        level: e.target.value\n                                                    }),\n                                                className: \"w-full mt-1 p-2 border border-gray-300 rounded-md\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"novice\",\n                                                        children: \"Novice\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"intermediate\",\n                                                        children: \"Intermediate\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 367,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"advanced\",\n                                                        children: \"Advanced\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                        value: \"expert\",\n                                                        children: \"Expert\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 359,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5 text-yellow-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Focus Areas\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2\",\n                                children: (focusAreasByRole[role] || []).map((area)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>toggleFocusArea(area),\n                                        className: \"p-2 text-sm rounded-lg border transition-all \".concat(config.focusAreas.includes(area) ? \"bg-blue-100 border-blue-300 text-blue-800\" : \"bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100\"),\n                                        children: area\n                                    }, area, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 387,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            config.focusAreas.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"h-4 w-4 text-yellow-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-yellow-800\",\n                                        children: \"Select at least one focus area for better personalized coaching\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 403,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                lineNumber: 377,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                            className: \"flex items-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-5 w-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"Your Profile\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Experience & Skills\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2 mt-2\",\n                                        children: config.userProfile.experience.map((exp)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"cursor-pointer hover:bg-red-100\",\n                                                onClick: ()=>removeExperience(exp),\n                                                children: [\n                                                    exp,\n                                                    \" \\xd7\"\n                                                ]\n                                            }, exp, true, {\n                                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                                lineNumber: 424,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_5__.Input, {\n                                        placeholder: \"Add experience (press Enter)\",\n                                        className: \"mt-2\",\n                                        onKeyPress: (e)=>{\n                                            if (e.key === \"Enter\") {\n                                                addExperience(e.currentTarget.value);\n                                                e.currentTarget.value = \"\";\n                                            }\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 434,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Areas you want to improve\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                        value: config.userProfile.weaknesses.join(\", \"),\n                                        onChange: (e)=>updateUserProfile({\n                                                weaknesses: e.target.value.split(\",\").map((w)=>w.trim()).filter(Boolean)\n                                            }),\n                                        placeholder: \"e.g., System design, Communication, Problem solving...\",\n                                        rows: 2,\n                                        className: \"mt-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_6__.Label, {\n                                        className: \"text-sm font-medium\",\n                                        children: \"Your goals\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 460,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_7__.Textarea, {\n                                        value: config.userProfile.goals.join(\", \"),\n                                        onChange: (e)=>updateUserProfile({\n                                                goals: e.target.value.split(\",\").map((g)=>g.trim()).filter(Boolean)\n                                            }),\n                                        placeholder: \"e.g., Get hired at FAANG, Improve technical skills, Practice interviews...\",\n                                        rows: 2,\n                                        className: \"mt-1\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 461,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 419,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                lineNumber: 412,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    onClick: ()=>onStartSession(config),\n                    disabled: config.focusAreas.length === 0,\n                    className: \"bg-blue-600 hover:bg-blue-700 px-8 py-3 text-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"mr-2 h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this),\n                        \"Start AI Coaching Session\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                    lineNumber: 476,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                lineNumber: 475,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-start space-x-3 p-4 bg-blue-50 border border-blue-200 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_BarChart3_Brain_Heart_Info_Settings_Target_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        className: \"h-5 w-5 text-blue-600 mt-0.5\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-800\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"font-medium mb-1\",\n                                children: \"How AI Coaching Works:\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 490,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-1 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• AI analyzes your responses in real-time and adapts questions accordingly\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Emotional intelligence tracking helps optimize your stress and confidence levels\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Personalized feedback is generated based on your profile and performance\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        children: \"• Session results include detailed analytics and improvement recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\apps\\\\InterviewSpark\\\\apps\\\\web\\\\src\\\\components\\\\coaching\\\\AICoachingConfig.tsx\",\n        lineNumber: 182,\n        columnNumber: 5\n    }, this);\n}\n_s(AICoachingConfig, \"ndctoc5aDy6KHFjnbhB5vnS7FT0=\");\n_c = AICoachingConfig;\nvar _c;\n$RefreshReg$(_c, \"AICoachingConfig\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/coaching/AICoachingConfig.tsx\n"));

/***/ })

});